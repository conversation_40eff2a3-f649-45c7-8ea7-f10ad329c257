import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'financialPreAppraisal',
  title: 'Financial Pre-Appraisal',
  type: 'object',
  fields: [
    defineField({
      name: 'subtitle',
      title: 'Subtitle',
      type: 'text',
    }),
    defineField({
      name: 'retailPrices',
      title: 'Retail Prices',
      type: 'array',
      of: [
        {
          type: 'number',
          title: 'Retail Price',
          description: 'Calculations worked out automatically from retail price',
        },
      ],
      validation: (Rule) => Rule.min(1).max(4),
    }),
  ],
});
