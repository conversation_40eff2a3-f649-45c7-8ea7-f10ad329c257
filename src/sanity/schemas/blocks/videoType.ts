import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'video',
  title: 'Video',
  type: 'object',
  fields: [
    defineField({
      name: 'videoType',
      title: 'Video Source',
      type: 'string',
      options: {
        list: [
          { title: 'YouTube URL', value: 'youtube' },
          { title: 'Upload Video', value: 'file' },
        ],
        layout: 'radio',
      },
    }),
    defineField({
      name: 'alternativeBackgroundColor',
      title: 'Alternative Background Colour',
      description: 'Optional - For use inline with Rolex Rich Text content',
      type: 'boolean',
    }),
    defineField({
      name: 'youtubeUrl',
      title: 'YouTube URL',
      type: 'url',
      hidden: ({ parent }) => parent?.videoType !== 'youtube',
    }),
    defineField({
      name: 'videoFile',
      title: 'Video File',
      type: 'file',
      hidden: ({ parent }) => parent?.videoType !== 'file',
      options: {
        accept: 'video/*',
      },
    }),
  ],
});
