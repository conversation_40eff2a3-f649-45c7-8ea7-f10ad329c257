import { defineType } from 'sanity';

export default defineType({
  name: 'featuredItems',
  title: 'Featured Items',
  type: 'object',
  fields: [
    {
      name: 'items',
      title: 'Items',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
            },
            {
              name: 'href',
              title: 'Link',
              type: 'string',
            },
            {
              name: 'image',
              title: 'Image',
              type: 'image',
              options: {
                hotspot: true,
              },
            },
          ],
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'items.0.title',
    },
    prepare({ title }) {
      return {
        title: 'Featured Items',
        subtitle: title ? `First item: ${title}` : 'No items added',
      };
    },
  },
});
