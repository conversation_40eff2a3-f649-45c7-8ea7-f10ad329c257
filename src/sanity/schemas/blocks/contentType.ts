import { defineArrayMember, defineField } from 'sanity';

export default defineField({
  name: 'content',
  type: 'array',
  title: 'Page Content',
  group: 'content',
  validation: (Rule) => Rule.required().min(1),
  of: [
    defineArrayMember({
      name: 'proseTitle',
      type: 'proseTitle',
    }),
    defineArrayMember({
      name: 'heroCarousel',
      type: 'heroCarousel',
    }),
    defineArrayMember({
      name: 'featuredItems',
      type: 'featuredItems',
    }),
    defineArrayMember({
      name: 'imageCta',
      type: 'imageCta',
    }),
    defineArrayMember({
      name: 'imageGridCols',
      type: 'imageGridCols',
    }),
    defineArrayMember({
      name: 'fullWidthCta',
      type: 'fullWidthCta',
    }),
    defineArrayMember({
      name: 'imageContent',
      type: 'imageContent',
    }),
    defineArrayMember({
      name: 'contactForm',
      type: 'contactForm',
    }),
    defineArrayMember({
      name: 'video',
      type: 'video',
    }),
    defineArrayMember({
      name: 'financialPreAppraisal',
      type: 'financialPreAppraisal',
    }),
    // * Rolex Specific Blocks
    defineArrayMember({
      name: 'rolexPageTitle',
      type: 'rolexPageTitle',
    }),
    defineArrayMember({
      name: 'rolexCard',
      type: 'rolexCard',
    }),
    defineArrayMember({
      name: 'rolexCardGroup',
      type: 'rolexCardGroup',
    }),
    defineArrayMember({
      name: 'rolexExploreCarousel',
      type: 'rolexExploreCarousel',
    }),
    defineArrayMember({
      name: 'rolexRichText',
      type: 'rolexRichText',
    }),
    defineArrayMember({
      name: 'rolexRichTextCalibre',
      type: 'rolexRichTextCalibre',
    }),
    defineArrayMember({
      name: 'rolexHomeBanner',
      type: 'rolexHomeBanner',
    }),
    defineArrayMember({
      name: 'rolexContactCard',
      type: 'rolexContactCard',
    }),
    defineArrayMember({
      name: 'rolexContactForm',
      type: 'rolexContactForm',
    }),
    defineArrayMember({
      name: 'rolexWatchGroup',
      type: 'rolexWatchGroup',
    }),
    defineArrayMember({
      name: 'rolexPostGroup',
      type: 'rolexPostGroup',
    }),
    defineField({
      name: 'rolexBookingType',
      type: 'rolexBookingType',
    }),
    defineArrayMember({
      name: 'richText',
      type: 'richText',
    }),
    defineArrayMember({
      name: 'rolexModelHero',
      type: 'rolexModelHero',
    }),
    defineArrayMember({
      name: 'rolexFeatureList',
      type: 'rolexFeatureList',
    }),
    defineArrayMember({
      name: 'rolexAccordionType',
      type: 'rolexAccordionType',
    }),
    defineArrayMember({
      name: 'rolexContactUsAccordionType',
      type: 'rolexContactUsAccordionType',
    }),
    defineArrayMember({
      name: 'rolexProductList',
      type: 'rolexProductList',
    }),
  ],
});
