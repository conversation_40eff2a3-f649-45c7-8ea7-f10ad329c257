import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'imageGridCols',
  title: 'Image Grid Cols',
  type: 'object',
  fields: [
    defineField({
      name: 'images',
      title: 'Images',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            defineField({
              name: 'image',
              title: 'Image',
              type: 'image',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'alt',
              title: 'Alt Text',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'caption',
              title: 'Caption',
              type: 'string',
            }),
          ],
        },
      ],
      validation: (Rule) => Rule.required().min(1),
    }),
    defineField({
      name: 'columns',
      title: 'Number of Columns',
      type: 'number',
      options: {
        list: [
          { title: '1 Column', value: 1 },
          { title: '2 Columns', value: 2 },
          { title: '3 Columns', value: 3 },
          { title: '4 Columns', value: 4 },
        ],
        layout: 'radio',
      },
      validation: (Rule) => Rule.required().min(1).max(4),
      initialValue: 1,
    }),
    defineField({
      name: 'spacing',
      title: 'Spacing Between Images',
      type: 'number',
      options: {
        list: [
          { title: 'Small', value: 2 },
          { title: 'Medium', value: 4 },
          { title: 'Large', value: 8 },
        ],
        layout: 'radio',
      },
      validation: (Rule) => Rule.required(),
      initialValue: 4,
    }),
  ],
  preview: {
    select: {
      images: 'images',
      columns: 'columns',
    },
    prepare({ images, columns }) {
      return {
        title: `Image Block - ${columns} ${columns === 1 ? 'Column' : 'Columns'}`,
        subtitle: `${images?.length || 0} ${images?.length === 1 ? 'image' : 'images'}`,
        media: images?.[0]?.image,
      };
    },
  },
});
