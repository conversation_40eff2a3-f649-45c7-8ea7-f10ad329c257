import { defineType } from 'sanity';

export default defineType({
  name: 'richText',
  title: 'Rich Text',
  type: 'object',
  fields: [
    {
      name: 'content',
      title: 'Content',
      type: 'array',
      of: [
        { type: 'block' },
        { type: 'image' },
        {
          name: 'imageGrid',
          title: 'Image Grid',
          type: 'object',
          fields: [
            {
              name: 'leftImage',
              title: 'Left Image',
              type: 'image',
            },
            {
              name: 'rightImage',
              title: 'Right Image',
              type: 'image',
            },
          ],
        },
      ],
    },
  ],
});
