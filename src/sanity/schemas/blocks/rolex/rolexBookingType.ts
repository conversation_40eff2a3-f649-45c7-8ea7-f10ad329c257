import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'rolexBookingType',
  title: 'Rolex Booking System',
  type: 'object',
  fields: [
    defineField({
      name: 'visible',
      type: 'boolean',
    }),
    defineField({
      name: 'cards',
      title: 'Cards',
      type: 'array',
      of: [{ type: 'rolexCard' }],
      validation: (Rule) => Rule.required().min(1).max(2),
    }),
  ],
});
