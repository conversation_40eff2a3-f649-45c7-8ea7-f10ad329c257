import { TextAlign } from '@/components/blocks/rolex/RolexRichText';
import { TextAlignCenter } from '@/components/ui/icons/textAlignCenterIcon';
import { TextAlignLeft } from '@/components/ui/icons/textAlignLeftIcon';
import { TextAlignRight } from '@/components/ui/icons/textAlignRightIcon';
import { SquareIcon } from '@sanity/icons';
import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'rolexRichText',
  title: 'Rolex Rich Text',
  type: 'object',
  fields: [
    defineField({
      name: 'alternativeBackgroundColor',
      title: 'Rolex Alternative Background Colour',
      description: 'Some Rolex text sections have a different background color, this applies it.',
      type: 'boolean',
    }),
    defineField({
      name: 'content',
      title: 'Content',
      type: 'array',
      of: [
        {
          type: 'block',
          styles: [
            { title: 'Normal', value: 'normal' },
            { title: 'Heading 1', value: 'h1' },
            { title: 'Heading 2', value: 'h2' },
            { title: 'Heading 3', value: 'h3' },
            { title: 'Quote', value: 'blockquote' },
          ],
          marks: {
            decorators: [
              { title: 'Strong', value: 'strong' },
              { title: 'Emphasis', value: 'em' },
              { title: 'Underline', value: 'underline' },
              {
                title: 'Left',
                value: 'left',
                icon: TextAlignLeft,
                component: (props) => TextAlign(props),
              },
              {
                title: 'Center',
                value: 'center',
                icon: TextAlignCenter,
                component: (props) => TextAlign(props),
              },
              {
                title: 'Right',
                value: 'right',
                icon: TextAlignRight,
                component: (props) => TextAlign(props),
              },
            ],
            annotations: [
              {
                name: 'link',
                type: 'object',
                title: 'Link',
                fields: [
                  {
                    name: 'href',
                    type: 'url',
                    title: 'URL',
                  },
                ],
              },
              {
                name: 'button',
                type: 'object',
                title: 'Button Link',
                icon: SquareIcon,
                fields: [
                  {
                    name: 'href',
                    type: 'string',
                    title: 'URL or Path',
                  },
                ],
              },
            ],
          },
          lists: [
            { title: 'Bullet', value: 'bullet' },
            { title: 'Number', value: 'number' },
          ],
        },
        {
          name: 'imageGrid',
          title: 'Image Grid',
          type: 'object',
          fields: [
            {
              name: 'leftImage',
              title: 'Left Image',
              type: 'image',
            },
            {
              name: 'rightImage',
              title: 'Right Image',
              type: 'image',
            },
          ],
        },
        {
          name: 'inlineImage',
          type: 'image',
          fields: [
            {
              name: 'alt',
              type: 'string',
              title: 'Alternative Text',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'explicitWidth',
              type: 'number',
              title: 'Explicit Image Width',
              description: 'Optional - Use this for more granular control over image sizing',
            },
            {
              name: 'explicitHeight',
              type: 'number',
              title: 'Explicit Image Height',
              description: 'Optional - Use this for more granular control over image sizing',
            },
            {
              name: 'centerImage',
              type: 'boolean',
              initialValue: true,
            },
          ],
        },
        {
          name: 'fullWidthImage',
          type: 'image',
          fields: [
            {
              name: 'alt',
              type: 'string',
              title: 'Image Alternative Text',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'unconstrained',
              type: 'boolean',
              title: 'Extend to ends of page',
              description:
                'Optional - Full width images are constrained by the text containers max width, this ignores that and ensures the image reaches the edges of the page.',
            },
          ],
        },
        {
          name: 'quoteBlock',
          title: 'Quote Block',
          type: 'object',
          fields: [
            {
              name: 'quote',
              type: 'text',
              title: 'Quote',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'author',
              type: 'string',
              title: 'Author',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'year',
              type: 'string',
              title: 'Year',
              validation: (Rule) => Rule.required(),
            },
          ],
        },
      ],
    }),
  ],
});
