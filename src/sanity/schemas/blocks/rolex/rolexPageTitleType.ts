import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'rolexPageTitle',
  title: 'Rolex Page Title',
  type: 'object',
  fields: [
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
    }),
    defineField({
      name: 'subtitle',
      title: 'Subtitle',
      type: 'array',
      of: [
        {
          type: 'block',
          marks: {
            decorators: [{ title: 'Strong', value: 'strong' }],
            annotations: [
              {
                name: 'link',
                type: 'object',
                title: 'link',
                fields: [
                  {
                    name: 'href',
                    title: 'href',
                    type: 'string',
                  },
                ],
              },
            ],
          },
        },
      ],
    }),
  ],
});
