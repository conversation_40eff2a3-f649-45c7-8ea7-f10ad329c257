import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'rolexHomeBanner',
  title: 'Rolex Home Banner',
  type: 'object',
  description: 'Note - this type has no fields just check visible',
  fields: [
    defineField({
      name: 'isVisible',
      title: 'Is Visible?',
      type: 'boolean',
      description: 'Optional - Check only on the Charles Fox Home Page',
    }),
  ],
});
