import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'rolexWatchGroup',
  title: 'Rolex Watch Group',
  type: 'object',
  fields: [
    defineField({
      name: 'uiType',
      title: 'UI Type',
      type: 'string',
      description: 'Select different UI display types according to the situation',
      options: {
        list: [
          { title: 'New Watches list', value: 'newWatches' },
          { title: 'Series Watches List', value: 'seriesWatch' },
          { title: 'Banner', value: 'banner' },
          { title: 'Post', value: 'post' },
          { title: 'Booking', value: 'booking' },
          { title: 'Two-Columns layout', value: 'twoColumns' },
          { title: 'Multi-Column layout', value: 'multiColumns' },
        ],
        layout: 'radio',
      },
    }),
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
      description:
        'The types of watches found in this group, use `classic`, `professional`, or `perpetual` for main watches page and the watch collection name if on a watch page.',
      hidden: ({ parent }) => {
        return parent?.isNewWatches === true;
      },
    }),
  ],
});
