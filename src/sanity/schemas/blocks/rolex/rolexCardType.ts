import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'rolexCard',
  title: 'Rolex Card',
  type: 'object',
  fields: [
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
      description: 'Optional - for full width cards',
    }),
    defineField({
      name: 'image',
      title: 'Image',
      type: 'image',
    }),
    defineField({
      name: 'mobileImage',
      title: 'Mobile Image',
      type: 'image',
      description: 'Optional - image to be used on mobile devices',
    }),

    defineField({
      name: 'ctaSubheading',
      title: 'CTA Sub Heading',
      type: 'string',
      description: 'Optional - the small heading above the main one',
    }),
    defineField({
      name: 'ctaHeading',
      title: 'CTA Heading',
      type: 'string',
    }),
    defineField({
      name: 'ctaText',
      title: 'CTA Text',
      type: 'string',
      description: 'Optional - the green text at the bottom e.g. `Learn More`',
    }),
    defineField({
      name: 'ctaLink',
      title: 'CTA Link',
      type: 'string',
      description: 'Note - do not set this on the booking system type',
    }),
    defineField({
      name: 'uiType',
      title: 'UI Type',
      type: 'string',
      description: 'Select different UI display types according to the situation',
      options: {
        list: [
          { title: 'New Watches list', value: 'newWatches' },
          { title: 'Series Watches List', value: 'seriesWatch' },
          { title: 'Banner', value: 'banner' },
          { title: 'Post', value: 'post' },
          { title: 'Booking', value: 'booking' },
          { title: 'Two-Columns layout', value: 'twoColumns' },
          { title: 'Multi-Column layout', value: 'multiColumns' },
        ],
        layout: 'radio',
      },
    }),
  ],
});
