import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'rolexCardGroup',
  title: 'Rolex Card Group',
  type: 'object',
  fields: [
    defineField({
      name: 'uiType',
      title: 'UI Type',
      type: 'string',
      description: 'Select different UI display types according to the situation',
      options: {
        list: [
          { title: 'New Watches list', value: 'newWatches' },
          { title: 'Series Watches List', value: 'seriesWatch' },
          { title: 'Banner', value: 'banner' },
          { title: 'Post', value: 'post' },
          { title: 'Booking', value: 'booking' },
          { title: 'Two-Columns layout', value: 'twoColumns' },
          { title: 'Multi-Column layout', value: 'multiColumns' },
        ],
        layout: 'radio',
      },
    }),
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
    }),
    defineField({
      name: 'cards',
      title: 'Cards',
      type: 'array',
      of: [{ type: 'rolexCard' }],
      validation: (Rule) => Rule.required().min(1).max(3),
    }),
  ],
});
