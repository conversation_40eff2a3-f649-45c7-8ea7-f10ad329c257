import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'heroCarousel',
  title: 'Hero Carousel',
  type: 'object',
  fields: [
    defineField({
      name: 'slides',
      title: 'Slides',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            defineField({
              name: 'image',
              title: 'Desktop Image',
              type: 'image',
              validation: (Rule) =>
                Rule.required().custom((image) => {
                  if (!image?.asset?._ref) {
                    return true; // This allows the initial upload
                  }

                  return true;
                }),
            }),
            defineField({
              name: 'mobileImage',
              title: 'Mobile Image',
              type: 'image',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'alt',
              title: 'Alt Text',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'href',
              title: 'Link URL',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'hideTitle',
              title: 'Hide Title',
              description: 'Optional - Use if the image contains text content',
              type: 'boolean',
            }),
            defineField({
              name: 'hideTitleMobile',
              title: 'Hide Title on Mobile',
              description: 'Optional - Use if the image contains text content',
              type: 'boolean',
            }),
            defineField({
              name: 'isRolexSlide',
              title: 'Is Rolex Slide?',
              type: 'boolean',
              description: 'Optional - This alters the font of the slide',
            }),
            defineField({
              name: 'title',
              title: 'Title',
              type: 'string',
              description: 'Rolex Pages - The small title above the larger one',
            }),
            defineField({
              name: 'description',
              title: 'Description',
              type: 'string',
              description: 'Rolex Pages - The larger title',
            }),
            defineField({
              name: 'decreasePadding',
              title: 'Decrease Left Padding',
              description: 'Optional - Use if the extends too far',
              type: 'boolean',
            }),
          ],
        },
      ],
      validation: (Rule) => Rule.required().min(1),
    }),
  ],
});
