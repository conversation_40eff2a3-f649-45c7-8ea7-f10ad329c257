import { adjustYearIfBeforeApril } from '@/lib/utils';
import { defineField, defineType } from 'sanity';
import contentType from '../../blocks/contentType';

export default defineType({
  name: 'rolexWatch',
  title: 'Rolex Watch',
  type: 'document',
  groups: [
    {
      name: 'meta',
      title: 'Page Metadata',
      default: true,
    },
    {
      name: 'content',
      title: 'Page Content',
    },
  ],
  fields: [
    defineField({
      name: 'rmc',
      type: 'string',
      title: 'RMC',
      group: 'meta',
    }),
    defineField({
      name: 'ranking',
      type: 'number',
      title: 'Ranking',
      group: 'meta',
      description: 'Numeric ranking for sorting watches (lower numbers appear first)',
    }),
    defineField({
      name: 'title',
      type: 'string',
      title: 'Title',
      group: 'meta',
    }),
    defineField({
      name: 'releaseDate',
      type: 'date',
      title: 'Release Date',
      group: 'meta',
      description:
        'Note - if set in or after the month of August for a year it is added to the New Watches page, make sure to regenerate the slug if this changes',
    }),
    defineField({
      name: 'slug',
      type: 'slug',
      description: 'Just click generate for this one',
      group: 'meta',
      options: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        source: (doc: any) => {
          if (!doc.releaseDate) return '';

          const currentYear = adjustYearIfBeforeApril(new Date());
          const releaseDate = new Date(doc.releaseDate);
          const cutoffDate = new Date(`${currentYear}-08-31`);

          const baseSlug = `/rolex/watches/${doc.title.toLowerCase().replace(/\s+/g, '-').replace('rolex-', '')}`;

          if (releaseDate > cutoffDate) {
            return `/rolex/new-watches/${doc.title.toLowerCase().replace(/\s+/g, '-').replace('rolex-', '')}`;
          }

          return baseSlug;
        },
        slugify: (input) => input,
      },
    }),
    defineField({
      name: 'description',
      type: 'text',
      title: 'Description',
      group: 'meta',
    }),
    defineField({
      name: 'type',
      type: 'string',
      title: 'Type',
      group: 'meta',
      description: 'Optional - Specify the type of the watch (e.g., Oyster Perpetual, Submariner, etc.)',
    }),
    defineField({
      name: 'size',
      type: 'number',
      title: 'Size',
      group: 'meta',
      description: 'Optional - Specify the size of the watch in millimeters (e.g., 36, 41, etc.)',
    }),
    defineField({
      name: 'material',
      type: 'string',
      title: 'Material',
      group: 'meta',
      description: 'Optional - Specify the material of the watch (e.g., Stainless Steel, Gold, etc.)',
    }),
    defineField({
      name: 'modelCase',
      type: 'string',
      title: 'Model Case',
      group: 'meta',
      description: 'Model and specifications of the watch (e.g., Oyster, 41 mm, Oystersteel, etc.)',
    }),
    defineField({
      name: 'retailPrices',
      type: 'string',
      title: 'Retail Prices',
      group: 'meta',
      description: 'Rolex recommends the highest retail price',
    }),
    defineField({
      name: 'rolexHeaderImage',
      title: 'Rolex Header Image',
      type: 'image',
      description: 'Optional - header image for Rolex pages',
      group: 'content',
    }),
    defineField({
      name: 'rolexHeaderImageMobile',
      title: 'Rolex Header Image Mobile',
      type: 'image',
      description: 'Optional - mobile header image for Rolex pages',
      group: 'content',
    }),
    defineField({
      name: 'category',
      type: 'string',
      title: 'Category',
      group: 'meta',
      options: {
        list: [
          { title: 'Classic Watches', value: 'classic' },
          { title: 'Professional Watches', value: 'professional' },
          { title: 'Perpetual Watches', value: 'perpetual' },
        ],
      },
    }),
    defineField({
      name: 'image',
      type: 'image',
      title: 'Image',
      group: 'meta',
      description: 'Required - This is the image that should display on the Watches collection page',
    }),
    defineField({
      name: 'parentWatch',
      type: 'reference',
      title: 'Parent Watch',
      to: [{ type: 'rolexWatch' }],
      group: 'meta',
    }),
    defineField({
      name: 'relatedWatches',
      type: 'array',
      title: 'Related Watches',
      of: [{ type: 'reference', to: [{ type: 'rolexWatch' }] }],
      group: 'meta',
    }),
    contentType,
  ],
});
