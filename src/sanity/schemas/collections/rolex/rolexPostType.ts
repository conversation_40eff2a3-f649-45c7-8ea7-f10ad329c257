import { defineField, defineType } from 'sanity';
import contentType from '../../blocks/contentType';

export default defineType({
  name: 'rolexPost',
  title: 'World of Rolex Post',
  type: 'document',
  groups: [
    {
      name: 'meta',
      title: 'Page Metadata',
      default: true,
    },
    {
      name: 'content',
      title: 'Page Content',
    },
  ],
  fields: [
    defineField({
      name: 'title',
      type: 'string',
      title: 'Title',
      group: 'meta',
    }),
    defineField({
      name: 'publishDate',
      type: 'date',
      title: 'Published Date',
      group: 'meta',
      description: 'Note - you can set this in the future and the post will not display until then.',
    }),
    defineField({
      name: 'slug',
      type: 'slug',
      description: 'Just click generate for this one',
      group: 'meta',
      options: {
        source: (doc) => {
          // @ts-expect-error ambiguous any type
          const postSlug = doc.title
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/&/g, 'and')
            .replace(/:/g, '')
            .replace(/’/g, '');

          const baseSlug = `/rolex/world-of-rolex/${postSlug}`;

          return baseSlug;
        },
        slugify: (input) => input, // The transformation is already done in the source function
      },
    }),
    defineField({
      name: 'description',
      type: 'text',
      title: 'Description',
      group: 'meta',
    }),
    defineField({
      name: 'rolexHeaderImage',
      type: 'image',
      title: 'Header Image',
      group: 'meta',
      description: 'Required - This is the image that should display on the World of Rolex collection   page',
    }),
    defineField({
      name: 'rolexHeaderImageMobile',
      title: 'Mobile Header Image',
      type: 'image',
      group: 'meta',
      description: 'Required - Square mobile image for the collections page',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'image',
      type: 'image',
      title: 'Header Image',
      group: 'meta',
      description: 'Required - This is the image that should display on the World of Rolex collection   page',
    }),
    defineField({
      name: 'mobileImage',
      title: 'Mobile Header Image',
      type: 'image',
      group: 'meta',
      description: 'Required - Square mobile image for the collections page',
      validation: (Rule) => Rule.required(),
    }),
    contentType,
  ],
});
