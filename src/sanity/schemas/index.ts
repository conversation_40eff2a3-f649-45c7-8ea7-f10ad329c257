import { type SchemaTypeDefinition } from 'sanity';
import appraisalType from './blocks/appraisalType';
import contactFormType from './blocks/contactFormType';
import featuredItemsType from './blocks/featuredItemsType';
import fullWidthCtaType from './blocks/fullWidthCtaType';
import heroCarouselType from './blocks/heroCarouselType';
import imageContentType from './blocks/imageContentType';
import imageCtaType from './blocks/imageCtaType';
import imageGridColsType from './blocks/imageGridColsType';
import proseTitleType from './blocks/proseTitleType';
import richTextType from './blocks/richTextType';
import rolexAccordionType from './blocks/rolex/rolexAccordionType';
import rolexBookingType from './blocks/rolex/rolexBookingType';
import rolexCardGroupType from './blocks/rolex/rolexCardGroupType';
import rolexCardType from './blocks/rolex/rolexCardType';
import rolexContactCardType from './blocks/rolex/rolexContactCardType';
import rolexContactFormType from './blocks/rolex/rolexContactFormType';
import rolexExploreCarouselType from './blocks/rolex/rolexExploreCarouselType';
import rolexFeatureList from './blocks/rolex/rolexFeatureListType';
import rolexHomeBannerType from './blocks/rolex/rolexHomeBannerType';
import rolexModelHero from './blocks/rolex/rolexModelHeroType';
import rolexPageTitleType from './blocks/rolex/rolexPageTitleType';
import rolexPostGroup from './blocks/rolex/rolexPostGroupType';
import rolexRichTextType from './blocks/rolex/rolexRichTextType';
import rolexRichTextCalibreType from './blocks/rolex/rolexRichTextCalibreType';
import rolexWatchGroup from './blocks/rolex/rolexWatchGroupType';
import rolexProductList from './blocks/rolex/rolexProductListType';
import rolexContactUsAccordionType from './blocks/rolex/rolexContactUsAccordionType';
import videoType from './blocks/videoType';
import rolexPost from './collections/rolex/rolexPostType';
import rolexWatchType from './collections/rolex/rolexWatchType';
import metaType from './metaType';
import pageType from './pageType';

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    metaType,
    pageType,
    heroCarouselType,
    featuredItemsType,
    imageCtaType,
    proseTitleType,
    fullWidthCtaType,
    imageContentType,
    appraisalType,
    contactFormType,
    videoType,
    richTextType,
    imageGridColsType,
    // * Rolex Specific Types
    rolexPageTitleType,
    rolexCardType,
    rolexBookingType,
    rolexCardGroupType,
    rolexExploreCarouselType,
    rolexHomeBannerType,
    rolexRichTextType,
    rolexRichTextCalibreType,
    rolexContactCardType,
    rolexContactFormType,
    rolexModelHero,
    rolexFeatureList,
    rolexAccordionType,
    rolexContactUsAccordionType,
    // * Collection Types
    rolexWatchType,
    rolexWatchGroup,
    rolexPost,
    rolexPostGroup,
    rolexProductList,
  ],
};
