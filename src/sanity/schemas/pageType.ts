import { defineField, defineType } from 'sanity';
import contentType from './blocks/contentType';

export default defineType({
  type: 'document',
  name: 'page',
  title: 'Pages',
  groups: [
    {
      name: 'meta',
      title: 'Page Metadata',
      default: true,
    },
    {
      name: 'content',
      title: 'Page Content',
    },
  ],
  fields: [
    defineField({
      type: 'string',
      name: 'title',
      title: 'Page Title',
      validation: (Rule) => Rule.required(),
      group: 'meta',
    }),
    defineField({
      name: 'slug',
      title: 'URL Slug',
      description: 'Ensure you write the full path sans-domain e.g. `/rolex/world-of-rolex`',
      type: 'slug',
      options: {
        source: 'title',
      },
      group: 'meta',
    }),
    defineField({
      name: 'description',
      title: 'Page Description',
      type: 'text',
      description: 'Optional - will fall back to site description if not set',
      group: 'meta',
    }),
    defineField({
      type: 'image',
      title: 'Social Share Image',
      name: 'ogImage',
      description: 'Optional - will fall back to site OG image if not set',
      group: 'meta',
    }),
    defineField({
      name: 'is<PERSON><PERSON>',
      title: 'Is Prose?',
      type: 'boolean',
      description: 'Optional - check this if the page is long form text content like a blog post',
      group: 'content',
    }),
    defineField({
      name: 'isRolex',
      title: 'Is Rolex?',
      type: 'boolean',
      description: 'Optional - check this if the page is part of the Rolex section',
      group: 'content',
    }),
    defineField({
      name: 'rolexHeaderImage',
      title: 'Rolex Header Image',
      type: 'image',
      description: 'Optional - header image for Rolex pages',
      group: 'content',
      hidden: ({ document }) => !document?.isRolex,
    }),
    defineField({
      name: 'rolexHeaderImageMobile',
      title: 'Rolex Header Image Mobile',
      type: 'image',
      description: 'Optional - mobile header image for Rolex pages',
      group: 'content',
      hidden: ({ document }) => !document?.isRolex,
    }),
    defineField({
      name: 'rolexWatchCta',
      title: 'Rolex Watch CTA',
      type: 'rolexCard',
      description: 'This card displays at the bottom of World of Rolex posts',
      group: 'content',
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      hidden: ({ document }: { document?: any }) =>
        !document?.isRolex || document?.slug?.current !== '/rolex/world-of-rolex',
    }),
    contentType,
  ],
});
