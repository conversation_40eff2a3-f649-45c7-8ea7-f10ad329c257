import { defineField, defineType } from 'sanity';

export default defineType({
  type: 'document',
  name: 'siteMeta',
  title: 'Site Configuration',
  fieldsets: [{ name: 'google', title: 'Google Analytics' }],
  groups: [
    {
      name: 'meta',
      title: 'Site Info',
      default: true,
    },
    {
      name: 'og',
      title: 'Social Share Info',
    },
    {
      name: 'contact',
      title: 'Contact Information',
    },
    {
      name: 'google',
      title: 'Google Config',
      hidden: ({ document }) => !document?.isGoogleAnalyticsEnabled,
    },
  ],
  fields: [
    defineField({
      type: 'image',
      title: 'Image',
      name: 'brandLogo',
      description: 'The brands logo image',
      validation: (Rule) => Rule.required(),
      group: ['meta'],
    }),
    defineField({
      type: 'string',
      name: 'site_name',
      title: 'Site Name',
      group: ['og', 'meta'],
    }),
    defineField({
      type: 'url',
      title: 'URL',
      name: 'url',
      description: 'Most likely either the url of the page or its canonical url',
      validation: (Rule) => Rule.required(),
      group: ['og', 'meta'],
    }),
    defineField({
      type: 'string',
      title: 'Page Title',
      name: 'title',
      description:
        'Set the title Open Graph should use. In most situations, this should be different from the value of the title prop',
      validation: (Rule) => Rule.required(),
      group: ['og', 'meta'],
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
      },
      validation: (Rule) => Rule.required(),
      group: 'meta',
    }),
    defineField({
      type: 'image',
      title: 'Image',
      name: 'ogImage',
      description:
        'URL of the image that should be used in social media previews. If you define this, you must define two other OG basic properties as well: title and type.',
      validation: (Rule) => Rule.required(),
      group: ['og'],
    }),
    defineField({
      type: 'text',
      name: 'description',
      title: 'Describe This Site',
      group: ['meta', 'og'],
    }),
    defineField({
      type: 'string',
      name: 'themeColor',
      title: 'Brand Colour',
      group: ['meta'],
    }),
    defineField({
      type: 'boolean',
      name: 'isGoogleAnalyticsEnabled',
      title: 'Enable Google Analytics?',
      group: ['meta', 'google'],
      initialValue: false,
      options: {
        layout: 'checkbox',
      },
    }),
    defineField({
      type: 'string',
      name: 'googleanalyticsId',
      title: 'Google Analytics ID',
      fieldset: 'google',
      group: ['meta', 'google'],
    }),
    defineField({
      type: 'string',
      name: 'googleSiteVerificationId',
      title: 'Google site Verification ID',
      fieldset: 'google',
      group: ['meta', 'google'],
    }),
    defineField({
      name: 'contactInfo',
      title: 'Contact Information',
      type: 'object',
      group: ['contact'],
      fields: [
        defineField({
          name: 'phone',
          title: 'Phone Number',
          type: 'string',
        }),
        defineField({
          name: 'email',
          title: 'Email Address',
          type: 'string',
        }),
        defineField({
          name: 'coordinates',
          title: 'Coordinates',
          type: 'object',
          fields: [
            defineField({
              name: 'lat',
              title: 'Latitude',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'long',
              title: 'Longitude',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
          ],
        }),
        defineField({
          name: 'socialLinks',
          title: 'Social Links',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                defineField({
                  name: 'url',
                  title: 'URL',
                  type: 'url',
                  validation: (Rule) => Rule.required(),
                }),
                defineField({
                  name: 'platform',
                  title: 'Platform',
                  type: 'string',
                  validation: (Rule) => Rule.required(),
                }),
              ],
            },
          ],
        }),
        defineField({
          name: 'openingTimes',
          title: 'Opening Times',
          type: 'object',
          fields: [
            defineField({
              name: 'monday',
              title: 'Monday',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'tuesday',
              title: 'Tuesday',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'wednesday',
              title: 'Wednesday',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'thursday',
              title: 'Thursday',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'friday',
              title: 'Friday',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'saturday',
              title: 'Saturday',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'sunday',
              title: 'Sunday',
              type: 'string',
              validation: (Rule) => Rule.required(),
            }),
          ],
        }),
        defineField({
          name: 'address',
          title: 'Address',
          type: 'text',
        }),
      ],
    }),
  ],
});
