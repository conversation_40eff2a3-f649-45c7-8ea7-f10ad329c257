import { client } from '@/sanity/lib/client';
import { getImageDimensions, SanityAsset } from '@sanity/asset-utils';
import imageUrlBuilder from '@sanity/image-url';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';

// Create image URL builder
const builder = imageUrlBuilder(client);

// A utility function to generate image URLs with optional transformations
export const urlFor = (source: SanityImageSource) => {
  if (!source) return null;

  return {
    url: () => {
      const imageUrl = builder.image(source);
      return imageUrl.url();
    },
    dimensions: () => {
      const { aspectRatio, height, width } = getImageDimensions(source as SanityAsset);

      return {
        aspectRatio,
        width,
        height,
      };
    },
  };
};
