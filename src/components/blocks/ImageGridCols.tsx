'use client';

import { urlFor } from '@/sanity/lib/image';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { ImageGridColsBlockProps } from '../Builder';

// const getGridClasses = (columns: 1 | 2 | 3 | 4) => {
//   const baseClasses = 'grid gap-4 w-full';
//   const columnClasses = {
//     1: 'grid-cols-1',
//     2: 'grid-cols-1 md:grid-cols-2',
//     3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
//     4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
//   };
//   return `${baseClasses} ${columnClasses[columns]}`;
// };

// const getSpacingClasses = (spacing: 2 | 4 | 8, isRolexWatchesPath: boolean) => {
//   if (isRolexWatchesPath) {
//     return 'gap-1';
//   }

//   const spacingMap = {
//     2: 'gap-2',
//     4: 'gap-4',
//     8: 'gap-8',
//   };
//   return spacingMap[spacing];
// };

export default function ImageGridCols({ data }: { data?: ImageGridColsBlockProps }) {
  const pathname = usePathname();
  const isRolexWatchesPath = pathname?.includes('/rolex/watches/');

  return (
    <section className="hidden w-full bg-rolex-beige-400 py-0 md:block">
      <div className="mx-auto mb-[90px] flex w-full max-w-[unset] flex-nowrap gap-x-[5px] px-[8%]">
        {data?.images?.map((item, index, array) => {
          let roundingClasses = 'rounded-lg';

          if (isRolexWatchesPath) {
            if (index === 0) {
              roundingClasses = 'rounded-l-lg';
            } else if (index === array.length - 1) {
              roundingClasses = 'rounded-r-lg';
            } else {
              roundingClasses = '';
            }
          }

          return (
            <div key={item._key} className="group flex flex-col items-center justify-center">
              <div className={`relative w-full overflow-hidden ${roundingClasses}`}>
                {item.image && (
                  <Image
                    src={urlFor(item.image || { _type: 'image' })?.url() || ''}
                    alt={item.alt ?? ''}
                    width={400}
                    height={400}
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                )}
                {item.caption && <p className="text-center text-sm text-foreground-500/90">{item.caption}</p>}
              </div>
            </div>
          );
        })}
      </div>
    </section>
  );
}
