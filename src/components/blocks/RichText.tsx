import { cn } from '@/lib/utils';
import { urlFor } from '@/sanity/lib/image';
import { PortableText } from 'next-sanity';
import Image from 'next/image';
import Link from 'next/link';
import { ReactNode } from 'react';
import { PortableTextBlock } from 'sanity';
import { RichTextBlockProps } from '../Builder';

const components = {
  marks: {
    link: ({ value, children }: { value?: { href: string }; children: ReactNode }) => {
      return (
        <Link
          href={value?.href ?? '/'}
          className="transition-colors duration-300 hover:text-foreground-500"
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </Link>
      );
    },
  },
  types: {
    // @ts-expect-error - TS7031
    image: ({ value }) => {
      const image = urlFor(value?.src);

      return (
        <div className={cn('relative w-full overflow-hidden', 'pb-[56.25%]')}>
          <Image
            src={image?.url() as string}
            alt={value?.alt ?? 'Image'}
            fill
            className={cn('transition-transform duration-500 ease-out group-hover:scale-110', 'object-cover')}
          />
        </div>
      );
    },
  },
};

export default function RichText({ data }: { data?: RichTextBlockProps }) {
  return (
    <div className={cn('flex flex-col md:gap-12 py-8 md:flex-row md:items-center')}>
      <div className={cn('space-y-4')}>
        {data && <PortableText value={(data?.content as PortableTextBlock[]) || null} components={components} />}
      </div>
    </div>
  );
}
