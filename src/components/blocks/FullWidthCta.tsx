import React from 'react';
import { CtaButton } from './ImageCta';

interface FullWidthCtaProps {
  backgroundImage: string;
  title: string;
  description: string;
  ctaLink: string;
  ctaText: string;
}

const FullWidthCta: React.FC<FullWidthCtaProps> = ({ backgroundImage, title, description, ctaLink, ctaText }) => {
  return (
    <div
      className="relative h-96 w-full overflow-hidden bg-cover bg-center text-white md:h-screen"
      style={{ backgroundImage: `url(${backgroundImage})` }}
    >
      <div className="absolute inset-0 bg-black opacity-50"></div>
      <div className="relative top-16 z-10 mx-auto flex h-full flex-col space-y-12 px-4 md:inset-36">
        <div className="space-y-2 text-left md:max-w-4xl">
          <h2 className="text-balance text-2xl font-medium md:text-7xl">{title}</h2>
          <p className="text-base md:text-xl">{description}</p>
        </div>
        <CtaButton href={ctaLink} text={ctaText} isTransparent />
      </div>
    </div>
  );
};

export default FullWidthCta;
