import { Card, CardContent } from '@/components/ui/card';
import { placeholders } from '@/lib/placeholders';
import { urlFor } from '@/sanity/lib/image';
import { internalGroqTypeReferenceTo, SanityImageCrop, SanityImageHotspot } from '@/sanity/types';
import Image from 'next/image';
import Link from 'next/link';

export interface FeaturedItem {
  _type: null;
  _key: string;
  title?: string;
  href?: string;
  image?: {
    asset?: {
      _ref: string;
      _type: 'reference';
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset';
    };
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: 'image';
  };
  content: null;
}

export default function FeaturedItems({ items }: { items: FeaturedItem[] }) {
  const getHoverColorClass = (url: string) => {
    return url.toLowerCase().includes('rolex') ? 'group-hover:bg-rolex-green-500' : 'group-hover:bg-accent/80'; // Alternative hover color for non-Rolex pages
  };

  return (
    <section className="w-full">
      <div className="px-4 md:px-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          {items.map(({ _key, href, title, image }) => {
            const hoverColorClass = getHoverColorClass(href ?? '/');

            return (
              <Link key={_key} href={href ?? '/'}>
                <Card className="group cursor-pointer overflow-hidden transition-all duration-300 hover:shadow-lg">
                  <CardContent className="p-0">
                    <div className="relative aspect-1 overflow-hidden">
                      <Image
                        src={urlFor(image || { _type: 'image' })?.url() ?? placeholders.imageCta.image}
                        alt={title ?? placeholders.imageCta.description}
                        className="transition-transform duration-500 ease-out group-hover:scale-110"
                        fill
                      />
                      <div className="absolute inset-0 bg-black/0 transition-colors duration-300 group-hover:bg-black/20" />
                    </div>
                    <div className={`bg-accent p-4 transition-all duration-300 ${hoverColorClass}`}>
                      <span className="text-center font-heading text-lg font-bold text-background transition-transform duration-300">
                        {title}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>
      </div>
    </section>
  );
}
