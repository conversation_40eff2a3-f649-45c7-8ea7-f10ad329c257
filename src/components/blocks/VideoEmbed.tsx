import { cn } from '@/lib/utils';

export interface VideoData {
  _type: 'video';
  videoType: 'youtube' | 'file';
  youtubeUrl?: string;
  alternativeBackgroundColor?: boolean;
  videoFile?: {
    asset: {
      url: string;
    };
  };
}

const getYoutubeVideoId = (url: string) => {
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
  const match = url.match(regExp);
  return match && match[2].length === 11 ? match[2] : null;
};

export function VideoEmbed({ video }: { video: VideoData }) {
  const renderVideo = () => {
    if (video.videoType === 'youtube' && video.youtubeUrl) {
      const videoId = getYoutubeVideoId(video.youtubeUrl);

      return (
        <iframe
          className="px-4 md:px-0"
          src={`https://www.youtube.com/embed/${videoId}?autoplay=0`}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          width={1256}
          height={707}
          allowFullScreen
        ></iframe>
      );
    } else if (video.videoType === 'file' && video.videoFile) {
      return (
        <video className="w-full rounded-lg" controls autoPlay>
          <source src={video.videoFile.asset.url} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      );
    }
    return null;
  };

  return (
    <section className={cn('relative h-full pt-0', video.alternativeBackgroundColor && 'bg-rolex-beige-500')}>
      <div className="aspect-h-7 aspect-w-16 mx-auto">{renderVideo()}</div>
    </section>
  );
}
