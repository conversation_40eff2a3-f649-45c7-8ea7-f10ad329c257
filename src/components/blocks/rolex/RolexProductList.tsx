'use client';
import { getWatchProductListByCategory } from '@/sanity/lib/helpers';
import { useEffect, useState } from 'react';
import RolexProduct, { RolexProductProps } from './RolexProduct';
import { Button } from '@/components/ui/button';

// Transform function to ensure data conforms to RolexProductProps
const transformToCardProps = (data: any): RolexProductProps[] => {
  if (!data) return [];

  // Handle array data
  return data.map((item: any) => ({
    rmc: item.rmc,
    title: item.title || '',
    image: item.image || null,
    slug: item.slug?.current || '',
    ctaLink: item.ctaLink || (item.slug?.current ? `${item.slug.current}` : '#'),
    modelCase: item.modelCase || null,
    content: item.content || [],
  }));
};

export default function RolexProductList({ category }: { category: string }) {
  const [data, setData] = useState<RolexProductProps[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const itemsPerPage = 6;

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        const result = await getWatchProductListByCategory(`/rolex/watches/${category}/`, 1, itemsPerPage);
        // Transform data to match RolexProductProps before setting state
        const transformedData = transformToCardProps(result.watches);
        setData(transformedData);
        setTotalItems(result.total);
      } catch (error) {
        setError(error instanceof Error ? error : new Error('An unknown error occurred'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [category]);

  const loadMoreItems = async () => {
    if (currentPage * itemsPerPage >= totalItems) return;

    try {
      setIsLoading(true);
      const nextPage = currentPage + 1;

      const result = await getWatchProductListByCategory(`/rolex/watches/${category}/`, nextPage, itemsPerPage);

      if (!result || !result.watches) {
        console.error('Invalid result format:', result);
        throw new Error('Failed to load more items: Invalid response format');
      }

      const newItems = transformToCardProps(result.watches);

      // 保留现有数据，追加新数据
      setData((prevData) => {
        const combined = [...prevData, ...newItems];
        return combined;
      });

      setCurrentPage(nextPage);
    } catch (error) {
      console.error('Load more error:', error);
      setError(error instanceof Error ? error : new Error('Failed to load more items'));
    } finally {
      setIsLoading(false);
    }
  };

  const hasMorePages = totalItems > currentPage * itemsPerPage;

  if (error && data.length === 0) {
    return <div className="py-8 text-red-500">Error: {error.message}</div>;
  }

  return (
    <div className="m-0 mx-auto flex w-full flex-col justify-center px-[8%] pb-[60px] md:pb-[90px]">
      <div className="mx-auto my-0 flex flex-col items-center p-0 lg:max-w-[83%] lg:basis-10/12 2xl:max-w-[83.33%] 2xl:basis-10/12 xl:max-w-[83.33%] xl:basis-10/12">
        <div className="w-full p-0">
          <div className={`grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3`}>
            {data.length > 0 ? (
              data.map((item, index) => <RolexProduct key={`${item.slug}-${index}`} {...item} />)
            ) : (
              <p className="col-span-full py-8 text-center">No watches available in this category yet.</p>
            )}
          </div>

          {hasMorePages && (
            <div className="mt-5 flex justify-center rounded-none pt-5">
              <Button
                className="btn rolex-primary mt-2 rounded-full md:mt-6"
                onClick={loadMoreItems}
                disabled={isLoading}
              >
                {isLoading ? 'Loading...' : 'Load More'}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
