'use client';
import { cn } from '@/lib/utils';
import { urlFor } from '@/sanity/lib/image';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';
import { Img } from '@react-email/components';
import Link from 'next/link';

export interface RolexProductProps {
  rmc?: string;
  image: SanityImageSource | string;
  title?: string;
  ctaLink: string;
  slug?: string;
  modelCase?: string;
  content?: Array<any>;
}

export default function RolexProduct({ title, modelCase, slug, ctaLink, image }: RolexProductProps) {
  const renderContent = () => (
    <div className="relative flex w-auto flex-wrap border-0 p-0 text-center" key={slug}>
      <div className="h-fit w-full items-center no-underline">
        <div className="product-img h-[70%] w-full overflow-hidden pt-[5%]">
          {image && (
            <Img
              src={typeof image === 'string' ? image : urlFor(image)?.url() || ''}
              alt={title || 'Rolex watch'}
              className="mx-auto max-h-[350px] max-w-full object-contain"
            />
          )}
        </div>
        <div className="h-1/5 px-[50px] pb-[50px] text-left">
          <p className="mb-0 text-left text-[16px] font-bold leading-[1.1] text-rolex-brown">Rolex</p>
          <h3 className="m-0 p-0 text-left text-[24px] font-bold leading-[1.2] text-rolex-brown">{title}</h3>
          <span className="mt-[5px] block w-full p-0 text-left text-[16px] leading-[1.1] text-rolex-black">
            {modelCase}
          </span>
        </div>
      </div>
    </div>
  );

  // Process ctaLink to remove an extra slash if it starts with //
  const processedCtaLink =
    typeof ctaLink === 'string' && ctaLink.startsWith('//') ? ctaLink.replace('//', '/') : ctaLink;

  return (
    <Link
      href={typeof processedCtaLink === 'string' ? processedCtaLink : '#'}
      className={cn('block size-full bg-[#f4efea] hover:bg-[#00000008]')}
    >
      {renderContent()}
    </Link>
  );
}
