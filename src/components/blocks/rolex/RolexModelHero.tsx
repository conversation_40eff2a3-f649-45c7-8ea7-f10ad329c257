'use client';
import { urlFor } from '@/sanity/lib/image';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';
import { PortableText, PortableTextBlock } from 'next-sanity';
import Image from 'next/image';
import Link from 'next/link';
import RolexModelHeroCarousel from './RolexModelHeroCarousel';
import { getSpecificWatchProductRetailPrice } from '@/sanity/lib/helpers';
import { usePathname } from 'next/navigation';
import * as React from 'react';

interface RolexModelHeroProps {
  heading: string;
  subheading?: string;
  body?: PortableTextBlock[];
  image: SanityImageSource;
}

interface Action {
  label: string;
  icon: string;
  href: string;
}

const actions: Action[] = [
  { label: '01202 557 633', icon: 'phone-brown-icon', href: 'tel:01202557633' },
  { label: '<EMAIL>', icon: 'mail-brown-icon', href: 'mailto:<EMAIL>' },
  { label: 'Send a message', icon: 'chat-brown-icon', href: '/rolex/contact-us' },
  { label: 'Visit us', icon: 'get-direction-brown-icon', href: 'https://maps.app.goo.gl/nvZu4HMcyMHoY9ZSA' },
];

export default function RolexModelHero({ heading, subheading, body, image }: RolexModelHeroProps) {
  const pathname = usePathname();

  const [retailPrice, setRetailPrice] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchData = async () => {
      const result = await getSpecificWatchProductRetailPrice(pathname);
      if (result && result.retailPrices) {
        setRetailPrice(result.retailPrices);
      }
    };

    fetchData();
  }, [pathname]);

  return (
    <div className="relative flex h-auto min-h-[550px] w-full flex-col items-center bg-rolex-beige-400 px-[8%] md:h-[calc(80vh-140px)] md:flex-row">
      <div className="absolute inset-0 mx-auto hidden aspect-[40/59] h-full w-auto md:block lg:mx-auto">
        <Image src={urlFor(image)?.url() ?? ''} alt={heading} width={600} height={800} />
      </div>

      <div className="mb-5 block basis-1/2 py-10 md:hidden">
        <RolexModelHeroCarousel />
      </div>

      <div className="mb-5 h-fit w-full max-w-full md:mb-0 md:w-[33%]">
        <div>
          <h1 className="body24-bold mb-0">{heading}</h1>
          {subheading && <p className="headline50 mt-0 py-[10px]">{subheading}</p>}
        </div>
        <div className="body20-light">{body && <PortableText value={body} />}</div>
        <div className="body20-light relative flex h-[32px] items-center">
          {retailPrice && !isNaN(Number(retailPrice))
            ? Number(retailPrice).toLocaleString('en-GB', {
                style: 'currency',
                currency: 'GBP',
                maximumFractionDigits: 2,
                minimumFractionDigits: 2,
              })
            : ''}
          <div className="group relative ml-2">
            <Image src={`/icons/information-black-icon.svg`} width={15} height={15} alt={'icon'} />
            <div className="body20-light invisible absolute left-0 top-4 z-10 w-[440px] bg-[#f9f7f4] p-[10px] opacity-0 shadow-md transition-all duration-200 group-hover:visible group-hover:opacity-100">
              Suggested retail price inclusive of V.A.T.
              <br />
              The suggested retail price can be modified at any time without notice.
            </div>
          </div>
        </div>

        {actions.length > 0 && (
          <div className="flex flex-wrap items-center gap-5 md:mt-[50px]">
            {actions.map((action, i) => (
              <Link
                key={i}
                href={action.href}
                className="legend18-bold flex items-center gap-2 text-rolex-brown transition-colors hover:text-rolex-green-500"
              >
                <div className="rounded-full bg-white p-2.5">
                  <Image src={`/icons/${action.icon}.svg`} width={14} height={14} alt={action.label} />
                </div>
                <span className="hidden sm:block">{action.label}</span>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
