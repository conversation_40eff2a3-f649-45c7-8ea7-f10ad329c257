'use client';

import { ChevronRight } from 'lucide-react';
import { PortableText, PortableTextBlock, PortableTextMarkComponentProps } from 'next-sanity';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';

const components = {
  block: {
    normal: (props: { children?: React.ReactNode }) => <p className="body20-light my-0">{props.children}</p>,
  },
  marks: {
    strong: (props: { children?: React.ReactNode }) => <strong className="body20">{props.children}</strong>,
    link: ({ value, children }: PortableTextMarkComponentProps) => {
      return (
        <Link
          href={value?.href}
          className="group space-x-0 text-rolex-green-500 transition-colors hover:text-rolex-green-400"
        >
          {children} <ChevronRight className="-ml-1.5 inline size-5 transition-transform group-hover:translate-x-0.5" />
        </Link>
      );
    },
  },
};

export default function RolexPageTitle({ title, subtitle }: { title: string; subtitle?: PortableTextBlock[] }) {
  const pathname = usePathname();
  const showNewWatches = pathname?.startsWith('/rolex/new-watches');

  return (
    <div className="mx-auto flex flex-col gap-6 px-[8%] py-12 sm:flex-col sm:py-24 md:flex-row lg:flex-row xl:flex-row">
      <h1 className="headline50 xs:pb-6 xs:max-w-full xs:basis-full flex-none text-wrap  !font-rolex !font-bold md:max-w-[41.6666666%] md:basis-5/12">
        {showNewWatches && <p className="body20-light max-w-xl font-bold">New Watches 2025</p>}
        <p className="headline50">{title}</p>
      </h1>
      <div className="hidden h-1 p-0 sm:max-w-0 sm:basis-0 md:block  md:max-w-[8.3333333333%] md:basis-1/12 lg:max-w-[8.3333333333%] lg:basis-1/12"></div>
      <div className="body20-light h-auto max-w-xl p-0 align-top md:ml-auto md:max-w-[41.6666666667%] md:basis-5/12">
        <PortableText value={subtitle ?? []} components={components} />
      </div>
      <div className="hidden h-1 p-0 sm:max-w-0 sm:basis-0 md:block  md:max-w-[8.3333333333%] md:basis-1/12 lg:max-w-[8.3333333333%] lg:basis-1/12"></div>
    </div>
  );
}
