'use client';

import { useEffect } from 'react';
import Script from 'next/script';

export default function RolexHomeBanner() {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.rlxSmrtClck =
        window.rlxSmrtClck ||
        // @ts-expect-error implicit any
        function (p) {
          delete window.rlxSmrtClck;
          p.create(document.getElementById('rlxSmartClock'), [
            'd4623e54966ef3265128e1b1fb748099',
            'en',
            'https://www.charlesfoxjewellers.com/rolex',
            'richright',
            'light',
            'gold',
          ]);
        };
    }
  }, []);

  return (
    <section className="py-4">
      <div id="rlxSmartClock" className="py-4" />
      <Script id="rlxBanner" strategy="afterInteractive">
        {`
        (function (b, c, a, d, f, g, h, k, l, m, n) {
          b[d] = b[d] || function (p) {
            delete b[d];
            p.create(c.getElementById(f), [g, h, k, l, m, n]);
          };
          var e = c.getElementsByTagName(a)[0];
          a = c.createElement(a);
          a.async = !0;
          a.src = "//clock.rolex.com/smart-clock/static/js/invoker.js";
          e.parentNode.insertBefore(a, e);
        })(
          window,
          document,
          "script",
          "rlxSmrtClck",
          "rlxSmartClock",
          "d4623e54966ef3265128e1b1fb748099",
          "en",
          "https://www.charlesfoxjewellers.com/rolex",
          "richright",
          "light",
          "gold"
        );
      `}
      </Script>
    </section>
  );
}
