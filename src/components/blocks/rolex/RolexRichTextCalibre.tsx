import { cn } from '@/lib/utils';
import { urlFor } from '@/sanity/lib/image';
import { PortableText, PortableTextBlock, PortableTextReactComponents } from '@portabletext/react';
import Image from 'next/image';
import React from 'react';
import RolexFeatureList from './RolexFeatureList';
import RolexModelHero from './RolexModelHero';
import { Img } from '@react-email/components';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const TextAlign = (props: any) => {
  return <div style={{ textAlign: props.value ? props.value : 'left', width: '100%' }}>{props.children}</div>;
};

const components: Partial<PortableTextReactComponents> = {
  block: {
    h1: ({ children }) => (
      <div className="m-0 flex w-full justify-center px-[8%]">
        <div className="max-w-full basis-full md:max-w-[50%] md:basis-1/2">
          <h1 className="headline50 not-first:text-4xl text-rolex-gold mb-0 font-bold">{children}</h1>
        </div>
      </div>
    ),
    h2: ({ children }) => (
      <div className="m-0 flex w-full justify-center px-[8%]">
        <div className="max-w-full basis-full md:max-w-[50%] md:basis-1/2">
          <h1 className="headline36 not-first:text-4xl text-rolex-gold mb-0 font-bold">{children}</h1>
        </div>
      </div>
    ),
    h3: ({ children }) => (
      <div className="m-0 flex w-full justify-center px-[8%]">
        <div className="max-w-full basis-full md:max-w-[50%] md:basis-1/2">
          <h1 className="headline30 not-first:text-4xl text-rolex-gold mb-0 font-bold">{children}</h1>
        </div>
      </div>
    ),
    h4: ({ children }) => (
      <div className="m-0 flex w-full justify-center px-[8%]">
        <div className="max-w-full basis-full md:max-w-[50%] md:basis-1/2">
          <h1 className="headline26 not-first:text-4xl text-rolex-gold mb-0 font-bold">{children}</h1>
        </div>
      </div>
    ),
    // <p></p>
    normal: ({ children }) => (
      <div className="m-0 mt-5 flex w-full justify-center px-[8%]">
        <div className="max-w-full basis-full md:max-w-[50%] md:basis-1/2">
          <p className="body20-light m-0 min-h-[20px]">{children}</p>
        </div>
      </div>
    ),
  },
  types: {
    inlineImage: ({ value }) => {
      const image = urlFor(value.asset);
      const { width, height } = image?.dimensions() ?? {};
      const className = [];
      if (value.widthRatio === '5/6') {
        className.push('md:max-w-[83.3333333%] md:basis-5/6 max-w-full basis-full');
      }
      if (value.widthRatio === '1/2') {
        className.push('md:max-w-[50%] md:basis-1/2 max-w-full basis-full');
      }
      return (
        <div className="mb-[20px] mt-10 flex w-full flex-col px-4 py-0 sm:m-0 sm:px-[8%] sm:py-[60px] md:m-0 md:py-[60px] lg:py-[90px]">
          <Image
            src={urlFor(value.asset)?.url() as string}
            alt={value.alt}
            width={width}
            height={height}
            className={cn(`my-0`, value.centerImage && 'mx-auto', ...className)}
          />
        </div>
      );
    },
    fullWidthImage: ({ value }) => {
      const image = urlFor(value.asset);
      const { width, height } = image?.dimensions() ?? {};

      return (
        <Image
          src={image?.url() as string}
          alt={value.alt}
          width={value.explicitWidth ?? width}
          height={value.explicitHeight ?? height}
          className={cn(
            'w-screen relative object-cover left-1/2 -translate-x-1/2',
            value.unconstrained ? 'max-w-none' : 'my-12 max-w-full'
          )}
        />
      );
    },
    imageGrid: ({ value }) => {
      const leftImage = urlFor(value.leftImage);
      const rightImage = urlFor(value.rightImage);

      return (
        <div className="gap-0.25 relative left-1/2 !mt-[30px] mb-[30px] grid w-full basis-full -translate-x-1/2 grid-cols-1 px-4 sm:mb-[60px] sm:w-[83%] sm:basis-[83%] sm:gap-0.5 sm:px-[8%] md:mb-[90px] md:grid-cols-2">
          <Image src={leftImage?.url() as string} alt="Left image" width={850} height={950} className="!my-0 w-full" />
          <Image
            src={rightImage?.url() as string}
            alt="Right image"
            width={850}
            height={950}
            className="!my-0 w-full"
          />
        </div>
      );
    },
    quoteBlock: ({ value }) => (
      <div className="mx-auto max-w-3xl">
        <blockquote className="border-l-0 font-['Georgia']">
          <p className="mb-8 text-4xl leading-relaxed">{value.quote}</p>
          <footer className="text-xl">
            <cite className="not-italic">{value.author}</cite>,{value.year}
          </footer>
        </blockquote>
      </div>
    ),
    rolexModelHero: ({ value }) => <RolexModelHero {...value} />,
    rolexFeatureList: ({ value }) => <RolexFeatureList {...value} />,
  },
  marks: {
    button: (props) => (
      <a href={props.value.href} className="btn rolex-primary no-underline">
        {props.children}
      </a>
    ),
    left: ({ children }) => <div className="text-left">{children}</div>,
    center: ({ children }) => <div className="w-full text-center">{children}</div>,
    right: ({ children }) => <div className="text-right">{children}</div>,
  },
};

interface RichTextCalibreProps {
  value: PortableTextBlock[];
  isAlternativeBackgroundColor?: boolean;
  modelAvailability?: boolean;
}

const RolexRichTextCalibre: React.FC<RichTextCalibreProps> = ({
  value,
  isAlternativeBackgroundColor,
  modelAvailability,
}) => {
  // Check if the last block is a fullWidthImage
  const isLastBlockFullWidthImage = value[value.length - 1]?._type === 'fullWidthImage';
  return (
    <section
      className={cn(
        'pt-0 pb-8 sm:pb-16 my-0 w-full',
        isAlternativeBackgroundColor && 'bg-rolex-beige-500',
        isLastBlockFullWidthImage && 'pb-0' // Remove bottom padding if last block is fullWidthImage
      )}
    >
      <div className="prose prose-rolex mx-auto max-w-full basis-full">
        <PortableText value={value} components={components} />
      </div>

      {modelAvailability && (
        <div className="mx-[8%] mt-[90px] flex max-w-full flex-col items-center bg-[#f4efea] py-[90px]">
          <div className="mb-10 flex justify-center">
            <Img src="https://charlesfoxjewellers.com/icons/crown.png" alt="Crown Logo" style={{ width: 60 }} />
          </div>
          <div className="max-w-full basis-full self-center px-[7%] text-center md:max-w-[66.6666666%] md:basis-2/3">
            <h2 className="headline50 mb-5">Model availability</h2>
            <p className="body20-light mt-5 text-rolex-black">
              All Rolex watches are assembled by hand with the utmost care to ensure exceptional quality. Such high
              standards naturally restrict Rolex production capacity and, at times, the demand for Rolex watches
              outpaces this capacity.
            </p>
            <p className="body20-light mt-5 text-rolex-black">
              Therefore, the availability of certain models may be limited. New Rolex watches are exclusively sold by
              Official Rolex Retailers, who receive regular deliveries and independently manage the allocation and sales
              of watches to customers.
            </p>
            <p className="body20-light mt-0 text-rolex-black">
              Prestons is proud to be part of the worldwide network of Official Rolex Retailers and can provide
              information on the availability of Rolex watches.
            </p>
          </div>
        </div>
      )}
    </section>
  );
};

export default RolexRichTextCalibre;
