'use client';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import Image from 'next/image';

const accordionItems = [
  {
    id: 'item-1',
    title: 'Rolex Guarantee',
    content:
      "To ensure the precision and reliability of its timepieces, Rolex submits each watch after assembly to a stringent series of tests. All new Rolex watches purchased from one of the brand's Official Retailers come with a five-year international guarantee. When you buy a Rolex, the Official Retailer fills out and dates the Rolex guarantee card that certifies your watch's authenticity.",
  },
  {
    id: 'item-2',
    title: 'The green seal',
    content:
      'The five-year guarantee which applies to all Rolex models is coupled with the green seal, a symbol of its status as a Superlative Chronometer. This exclusive designation attests that the watch has successfully undergone a series of specific final controls by Rolex in its own laboratories according to its own criteria, in addition to the official COSC certification of its movement.',
  },
  {
    id: 'item-3',
    title: 'Rolex presentation box',
    content:
      "Every Rolex is delivered in a beautiful green presentation box that is both protector and keeper of the jewel that nests inside it. As the presentation box is also a symbol of giving, it is important, if you are purchasing a gift, that the recipient's first contact with their Rolex sets the stage for revealing what lies within.",
  },
];

export default function RolexGuarantee() {
  return (
    <div className="flex flex-col-reverse justify-between bg-rolex-beige-400 px-[8%] pb-[60px] pt-0 font-rolex sm:gap-8 md:flex-row md:pb-[90px]">
      {/* Image Section */}
      <div className="flex items-start justify-center md:w-1/2">
        <div className="relative w-full">
          <Image
            className="!relative block"
            src="/images/keep-exploring/rolex-guarantee.webp"
            fill
            alt="Rolex Guarantee Card"
            priority
          />
        </div>
      </div>

      {/* Accordion Section */}
      <div className="flex w-full items-start md:w-2/5">
        <Accordion type="single" collapsible defaultValue="item-1" className="w-full font-rolex">
          {accordionItems.map((item, index) => (
            <AccordionItem
              key={item.id}
              value={item.id}
              className={`border-rolex-grey-500 ${index === 0 ? 'border-y' : 'border-b'}`}
            >
              <AccordionTrigger className="body24-bold !px-0 py-[20px] text-rolex-brown hover:no-underline md:px-[25px]">
                {item.title}
              </AccordionTrigger>
              <AccordionContent className="text-rolex-brown">
                <p className="body20-light whitespace-normal px-0 leading-relaxed md:px-6">{item.content}</p>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  );
}
