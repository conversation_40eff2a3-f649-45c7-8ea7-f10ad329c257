import { cn } from '@/lib/utils';
import { urlFor } from '@/sanity/lib/image';
import { PortableText, PortableTextBlock, PortableTextReactComponents } from '@portabletext/react';
import Image from 'next/image';
import React from 'react';
import RolexFeatureList from './RolexFeatureList';
import RolexModelHero from './RolexModelHero';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const TextAlign = (props: any) => {
  return <div style={{ textAlign: props.value ? props.value : 'left', width: '100%' }}>{props.children}</div>;
};

const components: Partial<PortableTextReactComponents> = {
  block: {
    h1: ({ children }) => (
      <h1 className="rolex-text-40 not-first:text-4xl text-rolex-gold mb-6 font-bold">{children}</h1>
    ),
  },
  types: {
    inlineImage: ({ value }) => {
      const image = urlFor(value.asset);
      const { width, height } = image?.dimensions() ?? {};

      return (
        <Image
          src={urlFor(value.asset)?.url() as string}
          alt={value.alt}
          width={value.explicitWidth ?? width}
          height={value.explicitHeight ?? height}
          className={`my-12 ${value.centerImage && 'mx-auto'} `}
        />
      );
    },
    fullWidthImage: ({ value }) => {
      const image = urlFor(value.asset);
      const { width, height } = image?.dimensions() ?? {};

      return (
        <Image
          src={image?.url() as string}
          alt={value.alt}
          width={value.explicitWidth ?? width}
          height={value.explicitHeight ?? height}
          className={cn(
            'w-screen relative object-cover left-1/2 -translate-x-1/2',
            value.unconstrained ? 'max-w-none' : 'my-12 max-w-[85.5vw]'
          )}
        />
      );
    },
    imageGrid: ({ value }) => {
      const leftImage = urlFor(value.leftImage);
      const rightImage = urlFor(value.rightImage);

      return (
        <div className="gap-0.25 relative left-1/2 my-12 grid w-screen max-w-[76vw] -translate-x-1/2 grid-cols-1 px-4 sm:gap-0.5 md:grid-cols-2 md:px-0">
          <Image
            src={leftImage?.url() as string}
            alt="Left image"
            width={850}
            height={950}
            className="w-[125%] md:w-full"
          />
          <Image
            src={rightImage?.url() as string}
            alt="Right image"
            width={850}
            height={950}
            className="w-[125%] md:w-full"
          />
        </div>
      );
    },
    quoteBlock: ({ value }) => (
      <div className="mx-auto max-w-3xl">
        <blockquote className="border-l-0 font-['Georgia']">
          <p className="mb-8 text-4xl leading-relaxed">{value.quote}</p>
          <footer className="text-xl">
            <cite className="not-italic">{value.author}</cite>,{value.year}
          </footer>
        </blockquote>
      </div>
    ),
    rolexModelHero: ({ value }) => <RolexModelHero {...value} />,
    rolexFeatureList: ({ value }) => <RolexFeatureList {...value} />,
  },
  marks: {
    button: (props) => (
      <a href={props.value.href} className="btn rolex-primary no-underline">
        {props.children}
      </a>
    ),
    left: ({ children }) => <div className="text-left">{children}</div>,
    center: ({ children }) => <div className="w-full text-center">{children}</div>,
    right: ({ children }) => <div className="text-right">{children}</div>,
  },
};

interface RichTextProps {
  value: PortableTextBlock[];
  isAlternativeBackgroundColor?: boolean;
}

const RolexRichText: React.FC<RichTextProps> = ({ value, isAlternativeBackgroundColor }) => {
  // Check if the last block is a fullWidthImage
  const isLastBlockFullWidthImage = value[value.length - 1]?._type === 'fullWidthImage';

  return (
    <section
      className={cn(
        'py-16 my-0',
        isAlternativeBackgroundColor && 'bg-rolex-beige-500',
        isLastBlockFullWidthImage && 'pb-0' // Remove bottom padding if last block is fullWidthImage
      )}
    >
      <div className="prose prose-rolex mx-auto">
        <PortableText value={value} components={components} />
      </div>
    </section>
  );
};

export default RolexRichText;
