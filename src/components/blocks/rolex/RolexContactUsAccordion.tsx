'use client';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import RolexContactForm from './RolexContactForm';
import RolexBookingForm from './RolexBookingForm';
import Image from 'next/image';

export default function RolexContactUsAccordion() {
  const cards = [
    {
      _key: '108d32f7a4ac',
      _type: 'rolexCard',
      ctaLink: 'javascript:void(0)',
      ctaHeading: 'Discover the Rolex collections',
      ctaSubheading: 'Rolex watches',
      image: {
        _type: 'image',
        asset: { _ref: 'image-65dfd8da717a6074701f13bdf0d3640b68aeecc2-900x600-png', _type: 'reference' },
      },
      mobileImage: {
        _type: 'image',
        asset: { _ref: 'image-2576f7976141d9922cd41ed5a4314de2e5b271ef-720x922-png', _type: 'reference' },
      },
    },
    {
      _key: 'baa28fa35e44',
      _type: 'rolexCard',
      ctaLink: 'javascript:void(0)',
      ctaHeading: 'Bring or collect your watch for servicing',
      ctaSubheading: 'Rolex servicing',
      image: {
        _type: 'image',
        asset: { _ref: 'image-42885c32d1ea412f3092ccc9a67fe992f3569514-900x600-png', _type: 'reference' },
      },
      mobileImage: {
        _type: 'image',
        asset: { _ref: 'image-97cb5ae3d2dc1ae999488fd1ac36aea50c67946e-1200x750-jpg', _type: 'reference' },
      },
    },
  ];

  return (
    <div className="flex w-full items-start pb-[90px]">
      <div className="w-full px-[8%]">
        <h3 className="headline36 mb-5 text-rolex-brown">Contact us</h3>
        <Accordion type="single" collapsible defaultValue="item-1" className="w-full font-rolex">
          <AccordionItem key={'1'} value={'1'} className="border-y border-rolex-grey-500">
            <AccordionTrigger className="body20-bold !px-0 py-[20px] text-rolex-brown hover:no-underline md:px-[25px]">
              Send us a message
            </AccordionTrigger>
            <AccordionContent className="text-rolex-brown">
              <div className="relative w-full">
                <Image
                  src="https://cdn.sanity.io/images/v464wfjy/production/55156ee3cc4ce08f3105aa074e2c39d0113d6128-1999x940.jpg"
                  alt="Rolex Watches"
                  className="align-middle"
                  width={1999}
                  height={940}
                  priority
                />
              </div>
              <RolexContactForm></RolexContactForm>
            </AccordionContent>
          </AccordionItem>
          <AccordionItem key={'2'} value={'2'} className="border-b border-rolex-grey-500">
            <AccordionTrigger className="body20-bold !px-0 py-[20px] text-rolex-brown hover:no-underline md:px-[25px]">
              Book an appoinment
            </AccordionTrigger>
            <AccordionContent className="text-rolex-brown">
              <div className="relative w-full">
                <Image
                  src="https://cdn.sanity.io/images/v464wfjy/production/54e7c0443f96c98f7bb466fc829b83a5caf8e2d1-2880x840.webp"
                  alt="Rolex Watches"
                  className="align-middle"
                  width={2880}
                  height={840}
                  priority
                />
              </div>
              <RolexBookingForm cards={cards}></RolexBookingForm>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
}
