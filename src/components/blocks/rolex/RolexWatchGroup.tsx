'use client';
import { getNewWatches, getWatchGroup } from '@/sanity/lib/helpers';
import { useEffect, useState } from 'react';
import { RolexCardProps } from './RolexCard';
import RolexCardGroup from './RolexCardGroup';
import { cn } from '@/lib/utils';
import * as Type from '@/sanity/types';

// Transform function to ensure data conforms to RolexCardProps
const transformToCardProps = (data: any): RolexCardProps[] => {
  if (!data) return [];

  // Handle array data
  if (Array.isArray(data)) {
    return data.map((item) => ({
      title: item.title || '',
      image: item.image || null,
      slug: item.slug || null,
      ctaHeading: item.ctaHeading || item.title || 'View Details',
      ctaLink: item.ctaLink || item.slug?.current ? `${item.slug.current}` : '#',
    }));
  }

  // Handle collection data that has relatedWatches
  if (data.relatedWatches && Array.isArray(data.relatedWatches)) {
    return transformToCardProps(data.relatedWatches);
  }

  // Handle single object (unlikely but for completeness)
  return [
    {
      title: data.title || '',
      image: data.image || null,
      slug: data.slug || null,
      ctaHeading: data.ctaHeading || data.title || 'View Details',
      ctaLink: data.ctaLink || data.slug?.current ? `${data.slug.current}` : '#',
    },
  ];
};

export default function RolexWatchGroup({ group, uiType }: { group?: string; uiType: Type.RolexWatchGroup['uiType'] }) {
  const [data, setData] = useState<RolexCardProps[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        let rawData;
        if (uiType == 'newWatches') {
          rawData = await getNewWatches();
        } else if (uiType == 'seriesWatch' && group) {
          rawData = await getWatchGroup(group, false);
        }

        // Transform data to match RolexCardProps before setting state
        const transformedData = transformToCardProps(rawData);
        setData(transformedData);
      } catch (error) {
        setError(error instanceof Error ? error : new Error('An unknown error occurred'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [group, uiType]);

  if (isLoading) {
    return <div className="mx-auto w-full px-[8%]">Loading...</div>;
  }

  if (error) {
    return <div className="mx-auto w-full px-[8%]">Error: {error.message}</div>;
  }

  return (
    <div className={cn('mx-auto w-full')}>
      {group && (
        <h2 className="headline36 mx-auto w-full px-[8%] font-bold">
          {group ? group.charAt(0).toUpperCase() + group.slice(1) : 'New Watches'}
        </h2>
      )}
      {data.length > 0 ? <RolexCardGroup cards={data} uiType={uiType} /> : <p>No items in this group yet...</p>}
    </div>
  );
}
