'use client';
import CollapsibleOpeningTimes from '@/components/general/OpeningTimes';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { OpeningTimes } from '@/lib/utils';
import { getContactInformation } from '@/sanity/lib/helpers';
import { ContactInformationQueryResult } from '@/sanity/types';
import { AdvancedMarker, APIProvider, Map } from '@vis.gl/react-google-maps';
import Image from 'next/image';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';

export interface ContactCardProps {
  openingTimes: OpeningTimes;
  phone: string;
  coordinates: {
    lat: string;
    long: string;
  };
}

const ContactOption: React.FC<{
  href: string;
  newTab?: boolean;
  icon: string;
  text: string;
  width: number;
  height: number;
}> = ({ href, newTab, icon, text, width, height }) => {
  return (
    <Link
      href={href}
      target={newTab ? '_blank' : ''}
      rel={newTab ? 'noopener noreferrer' : ''}
      className="rolex-text-14 group flex items-center space-x-4 font-bold"
    >
      <div className="rounded-full bg-rolex-grey-400 p-2.5 transition-colors duration-300 group-hover:bg-rolex-grey-500/80">
        <Image src={`/icons/${icon}.svg`} width={width} height={height} alt={icon} />
      </div>
      <span className="legend16-light ml-2 transition-colors duration-300 group-hover:text-green-600">{text}</span>
    </Link>
  );
};

const RolexContactCard: React.FC = () => {
  const [data, setData] = useState<ContactInformationQueryResult | null>(null);
  const [position, sePosition] = useState<google.maps.LatLngLiteral | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const fetchedInfo = await getContactInformation();
        if (fetchedInfo && fetchedInfo.contactInfo) {
          setData(fetchedInfo);
          sePosition({
            lat: Number(fetchedInfo?.contactInfo?.coordinates?.lat),
            lng: Number(fetchedInfo?.contactInfo?.coordinates?.long),
          });
        }
      } catch (error) {
        throw error;
      }
    };
    fetchData();
  }, [data, position]);

  const apiKey = process.env.NEXT_PUBLIC_MAPS_KEY;

  return (
    <Card className="mx-auto h-[700px] w-full rounded-none shadow-none">
      <CardHeader className="p-0">
        <CardTitle className="sr-only">
          <span>Contact Charles Fox - Official Rolex Retailer</span>
        </CardTitle>
      </CardHeader>

      <CardContent className="grid h-full grid-cols-3 p-0">
        <div className="col-span-3 h-full bg-neutral-200 md:col-span-2">
          <APIProvider apiKey={apiKey as string}>
            <Map
              defaultCenter={position || { lat: 50.720190529578936, lng: -1.8768552164520418 }}
              defaultZoom={15}
              disableDefaultUI={true}
              // Styles editable in Google Maps API console
              mapId="3b7755e6897fdeaf"
              mapTypeId="roadmap"
            >
              <AdvancedMarker position={position} title="AdvancedMarker with custom html content.">
                <Image src="/icons/location-green.svg" alt="Rolex Map Pin" width={64} height={64} />
              </AdvancedMarker>
            </Map>
          </APIProvider>
        </div>
        <div className="col-span-3 mx-auto flex h-full max-w-xs flex-col items-start justify-center text-rolex-brown md:col-span-1">
          <div className="space-y-6">
            <div>
              <span className="legend16-light text-sm font-bold">Official Rolex Retailer</span>
              <h2 className="headline30">Charles Fox Jewellers</h2>
              <address className="legend16-light mt-3 font-bold not-italic">
                21-22 The Arcade
                <br />
                Bournemouth, Dorset
                <br />
                BH1 2AH
              </address>
            </div>

            <CollapsibleOpeningTimes openingTimes={data?.contactInfo?.openingTimes ?? {}} />

            <div className="flex flex-col items-start space-y-4">
              <ContactOption
                href={`tel:${data?.contactInfo?.phone}`}
                icon="phone"
                text={data?.contactInfo?.phone as string}
                width={18}
                height={18}
              />
              <ContactOption
                href="/rolex/contact-us/send-a-message"
                icon="mail"
                text="Send a message"
                width={18}
                height={14}
              />
              <ContactOption
                href={`https://www.google.com/maps/dir/?api=1&destination=${data?.contactInfo?.coordinates?.lat},${data?.contactInfo?.coordinates?.long}`}
                icon="directions"
                text="Get directions"
                width={18}
                height={18}
                newTab
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RolexContactCard;
