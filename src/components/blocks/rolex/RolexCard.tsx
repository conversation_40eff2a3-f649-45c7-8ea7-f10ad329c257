'use client';
import { cn } from '@/lib/utils';
import { urlFor } from '@/sanity/lib/image';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';
import Image from 'next/image';
import Link from 'next/link';
import { useBooking } from './RolexBookingSystem';
import { RolexWatchGroup } from '@/sanity/types';

export interface RolexCardProps {
  image: SanityImageSource | string;
  title?: string;
  ctaSubheading?: string;
  ctaHeading: string;
  ctaText?: string;
  ctaLink: string | (() => void);
  slug?: string;
  className?: string;
  mobileImage?: SanityImageSource | string;
  type?: string;
  size?: number;
  material?: string;
  description?: string;
  isBooking?: boolean;
  uiType?: RolexWatchGroup['uiType'];
  isFirstPost?: boolean;
}

export default function RolexCard({
  image,
  title,
  ctaSubheading = 'Rolex',
  ctaHeading,
  ctaText,
  ctaLink,
  className,
  description,
  uiType,
  isBooking,
  isFirstPost,
}: RolexCardProps) {
  const booking = useBooking();
  const handleBookingClick = () => {
    if (isBooking && booking) {
      const bookingPath = ctaHeading.includes('Discover') ? 'discover' : 'service';
      booking.setFormPath(bookingPath);
    }
  };

  // 计算标题的样式类名
  const getTitleClasses = () => {
    const baseClasses = 'headline36 p-0 mb-0 self-start font-rolex font-bold';

    if (uiType == 'newWatches' || uiType == 'twoColumns' || uiType == 'multiColumns') {
      return `${baseClasses} hidden`;
    }

    return baseClasses;
  };

  // 计算图片容器的样式类名
  const getImageContainerClasses = () => {
    const conditionalClasses = [];

    if (uiType == 'banner') {
      conditionalClasses.push('pb-[60%] sm:pb-[40%] ');
    }

    if (uiType == 'post') {
      if (isFirstPost) {
        conditionalClasses.push('h-[548px] sm:h-[448px]');
      } else {
        conditionalClasses.push('h-[348px] sm:h-[472px]');
      }
    }

    if (
      uiType == 'twoColumns' ||
      uiType == 'booking' ||
      uiType == 'multiColumns' ||
      uiType == 'seriesWatch' ||
      uiType == 'newWatches'
    ) {
      if (isFirstPost) {
        conditionalClasses.push('pb-[63%] sm:pb-[68%]');
      } else {
        conditionalClasses.push('pb-[63%] sm:pb-[68%]');
      }
    }

    return [...conditionalClasses].join(' ');
  };

  // 计算图片的样式类名
  const getImageClasses = () => {
    const baseClasses = 'transition-transform duration-500 ease-out group-hover:scale-110';
    const conditionalClasses = [];

    if (uiType == 'banner') {
      conditionalClasses.push('group-hover:scale-105 w-full');
    }

    if (uiType == 'post') {
      conditionalClasses.push('w-full max-w-full');
    }

    if (uiType == 'newWatches') {
      conditionalClasses.push('w-full h-full');
    }

    return [baseClasses, ...conditionalClasses].join(' ');
  };

  // 获取子标题文本
  const getSubheadingText = () => {
    if (uiType == 'newWatches') {
      return 'New Watches 2025';
    }
    return ctaSubheading;
  };

  // 获取主标题文本
  const getHeadingText = () => {
    if (uiType == 'newWatches') {
      return title;
    }
    return ctaHeading;
  };
  const _image = urlFor(image);
  const renderContent = () => (
    <>
      {uiType == 'banner' && <h2 className={getTitleClasses()}>{title}</h2>}
      <div className={cn('mt-[20px] mb-[10px] font-rolex relative w-full overflow-hidden', getImageContainerClasses())}>
        <Image
          src={typeof image === 'string' ? image : _image!.url()}
          alt={ctaText || ''}
          fill
          objectFit={'cover'}
          className={getImageClasses()}
        />
      </div>
      <div className="w-full">
        {uiType == 'banner' && (
          // 宽度撑满的Banner
          <>
            <span className="legend16-light block !font-bold">{getSubheadingText()}</span>
            <h3 className={'headline36'}>{getHeadingText()}</h3>
          </>
        )}

        {uiType == 'twoColumns' && (
          // 两列布局
          <>
            <h3 className={'headline36'}>{getHeadingText()}</h3>
            <p className="body20-light mt-1 text-black">{description}</p>
          </>
        )}

        {uiType == 'booking' && (
          // Booking
          <>
            <p className="legend16-light mt-1 text-left font-bold text-black">{getSubheadingText()}</p>
            <h3 className={'body20-bold text-left  text-black'}>{getHeadingText()}</h3>
          </>
        )}

        {uiType == 'multiColumns' && (
          // 多列布局
          <>
            <span className="legend16-light block !font-bold">{getSubheadingText()}</span>
            <h3 className={'headline36'}>{getHeadingText()}</h3>
            <p className="body20-light mt-1 text-black">{description}</p>
          </>
        )}

        {(uiType == 'post' || uiType == 'seriesWatch' || uiType == 'newWatches') && (
          // 手表分类、文章
          <>
            <span className="legend16-light block !font-bold">{getSubheadingText()}</span>
            <h3 className={uiType == 'post' ? 'headline36' : 'body20-bold'}>{getHeadingText()}</h3>
            <p className="body20-light mt-1 text-black">{description}</p>
          </>
        )}

        {
          // 【按钮】
          // 两列布局、多列布局、宽度撑满的Banner
          (uiType == 'banner' || uiType == 'twoColumns' || uiType == 'multiColumns') && ctaText && (
            <button className="rolex-label !mt-2 text-[16px] leading-4">{ctaText}</button>
          )
        }

        {
          // 【按钮】
          // 文章
          uiType == 'post' && <button className="rolex-label !mt-2 text-[16px] leading-4">Read More</button>
        }
      </div>
    </>
  );

  // Process ctaLink to remove an extra slash if it starts with //
  const processedCtaLink =
    typeof ctaLink === 'string' && ctaLink.startsWith('//') ? ctaLink.replace('//', '/') : ctaLink;

  // 计算容器的样式类名
  const getContainerClasses = () => {
    const conditionalClasses = [];

    // 如果是全宽布局
    if (uiType == 'banner') {
      conditionalClasses.push('col-span-full mx-auto mb-16 px-[8%]');
    }

    // 添加自定义类名
    if (className) {
      conditionalClasses.push(className);
    }

    return [...conditionalClasses].join(' ');
  };

  return (
    <div
      className={cn(
        'flex flex-col font-rolex h-full w-full group relative overflow-hidden duration-300 space-y-4',
        getContainerClasses()
      )}
    >
      <Link
        href={typeof processedCtaLink === 'string' ? processedCtaLink : '#'}
        className={cn('block')}
        onClick={isBooking ? handleBookingClick : undefined}
      >
        {renderContent()}
      </Link>
    </div>
  );
}
