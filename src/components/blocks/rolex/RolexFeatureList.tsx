import Image from 'next/image';
import Link from 'next/link';

interface RolexFeatureListProps {
  features: Array<{
    title: string;
    description: string;
  }>;
  cta: {
    link: string;
    text: string;
  };
}

export default function RolexFeatureList({ features, cta }: RolexFeatureListProps) {
  return (
    <div className="flex flex-col justify-center bg-rolex-beige-400">
      <div className="bg-rolex-beige-500 px-[8%] py-[90px]">
        <div className="mx-auto grid w-full auto-rows-min grid-cols-2 gap-8 md:grid-cols-3">
          {features.map((feature, i) => (
            <div key={i}>
              <h3 className="rolex-text-26 font-bold">{feature.title}</h3>
              <p className="body20-light mt-2">{feature.description}</p>
            </div>
          ))}
        </div>
        <div className="mx-auto mt-[40px] w-full max-w-screen-xl border-t-[1.5px] border-rolex-black/20 pt-[40px]">
          <Link
            href={cta.link}
            className="btn inline-flex items-center rounded-full bg-rolexButton text-[16px] text-white"
          >
            <Image
              src="/icons/download-back-icon.svg"
              width={14}
              height={14}
              alt="Download"
              className="mr-2 inline-block invert"
            />
            {cta.text}
          </Link>
        </div>
      </div>
    </div>
  );
}
