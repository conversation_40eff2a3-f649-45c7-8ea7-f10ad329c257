'use client';

import { getRolexPosts } from '@/sanity/lib/helpers';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';
import { useEffect, useState } from 'react';
import RolexCard from './RolexCard';
import RolexCardGroup from './RolexCardGroup';

export default function RolexPostGroup() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [posts, setPosts] = useState<any[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setIsLoading(true);
        const fetchedPosts = await getRolexPosts();
        setPosts(fetchedPosts);
      } catch (error) {
        setError(error instanceof Error ? error : new Error('An unknown error occurred'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (isLoading) {
    return <div className="mx-auto">Loading...</div>;
  }

  if (error) {
    return (
      <div className="mx-auto">
        Error:
        {error.message}
      </div>
    );
  }

  if (!posts || posts.length === 0) {
    return <div className="mx-auto">No items in this group yet...</div>;
  }
  const cards = posts
    .filter((post) => post.publishDate != null)
    .sort((a, b) => new Date(b.publishDate!).getTime() - new Date(a.publishDate!).getTime())
    .map(({ title, image, mobileImage, slug, publishDate, description }) => ({
      image: image as SanityImageSource,
      mobileImage: mobileImage as SanityImageSource,
      ctaSubheading: `Published on ${new Date(publishDate!).toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric',
      })}`,
      ctaHeading: title as string,
      ctaLink: slug?.current ?? '',
      description: description as string,
    }));

  return (
    <div className="mx-auto">
      <div className="px-[8%] pb-[30px] sm:pb-[60px] md:pb-[90px]">
        <RolexCard {...cards[0]} uiType="post" isFirstPost />
      </div>
      <RolexCardGroup cards={cards.slice(1)} uiType="post" />
    </div>
  );
}
