'use client';

import { sendRolexContactEmail } from '@/app/actions/sendEmail';
import { Checkbox } from '@/components/ui/checkbox';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ToastAction } from '@/components/ui/toast';
import { useToast } from '@/hooks/use-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import Image from 'next/image';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

const formSchema = z.object({
  message: z.string().min(10, {
    message: 'Message must be at least 10 characters long',
  }),
  title: z.string().min(1, { message: 'Title is required' }),
  firstName: z.string().min(1, { message: 'First name is required' }),
  lastName: z.string().min(1, { message: 'Last name is required' }),
  email: z.string().email({ message: 'Invalid email address' }),
  phone: z.string().optional(),
  country: z.string(),
  city: z.string(),
  storeAddress: z.string(),
  acceptTerms: z.boolean().refine((val) => val, {
    message: 'You must accept the terms and conditions',
  }),
});

export default function RolexContactForm() {
  const [step, setStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      message: '',
      title: 'Mr',
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      country: 'England',
      city: 'Bournemouth',
      storeAddress: '21 - 22 The Arcade, Bournemouth, Dorset, BH1 2AH',
      acceptTerms: false,
    },
  });

  const { toast } = useToast();

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);
    try {
      // Send email using Server Action
      console.log('Calling sendRolexContactEmail...');
      const response = await sendRolexContactEmail(values);
      console.log('Response received:', response);

      if (!response.success) {
        throw new Error(response.error || 'Form submission failed');
      }

      // If successful, move to thank you step
      setStep(3);
      toast({
        title: 'Success!',
        description: 'Your message has been sent successfully! Thank you.',
      });
    } catch (error) {
      console.error('Submission error:', error);
      toast({
        variant: 'destructive',
        title: 'Uh oh! Something went wrong.',
        description: 'There was a problem with your request.',
        action: (
          <ToastAction altText="Try again" onClick={form.handleSubmit(onSubmit)}>
            Try again
          </ToastAction>
        ),
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="w-full bg-white py-[90px] text-center">
      <div className="mx-auto w-1/2 basis-1/2">
        <div className="body24-bold mb-0">Send us a message</div>
        <h1 className="headline50 mb-[30px] text-rolex-brown">Please enter your message</h1>
        <p className="body20-light mx-auto mb-8 max-w-lg">
          Thank you for your interest in Rolex watches. Please enter your message and we will reply to you soon.
        </p>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            {step === 1 ? (
              <div>
                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea
                          className="text-rolex-32 placeholder:text-rolex-32 my-0 h-56 resize-none rounded-none border-none bg-[#f9f7f4] font-bold shadow-none placeholder:font-bold focus:border-rolex-brown focus:outline-none focus-visible:ring-0"
                          {...field}
                          rows={6}
                          placeholder="* Enter your message"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <button
                  type="button"
                  onClick={() => {
                    if (form.getValues('message').length < 10) {
                      toast({
                        title: 'Worning!',
                        description: 'Message must be at least 10 characters long.',
                      });
                      return;
                    }
                    setStep(2);
                  }}
                  className="btn rolex-primary mt-6"
                >
                  Next
                </button>
              </div>
            ) : step === 2 ? (
              <div className="space-y-6">
                <div className="grid grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-rolex-16 text-left font-bold">Title</FormLabel>
                        <FormControl>
                          <select
                            {...field}
                            className="my-0 w-full rounded-none border-x-0 border-b border-t-0 border-rolex-brown bg-transparent font-bold shadow-none focus:border-rolex-brown focus:outline-none focus-visible:ring-0"
                          >
                            <option value="Mr">Mr</option>
                            <option value="Miss">Miss</option>
                            <option value="Mrs">Mrs</option>
                            <option value="Ms">Ms</option>
                            <option value="Dr">Dr</option>
                          </select>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-rolex-16 text-left font-bold">First Name</FormLabel>
                        <FormControl>
                          <Input
                            className="my-0 rounded-none border-x-0 border-b border-t-0 border-rolex-brown bg-transparent font-bold shadow-none focus:border-rolex-brown focus:outline-none focus-visible:ring-0"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-rolex-16 text-left font-bold">Last Name</FormLabel>
                        <FormControl>
                          <Input
                            className="my-0 rounded-none border-x-0 border-b border-t-0 border-rolex-brown bg-transparent font-bold shadow-none focus:border-rolex-brown focus:outline-none focus-visible:ring-0"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-rolex-16 text-left font-bold">Email</FormLabel>
                      <FormControl>
                        <Input
                          className="my-0 rounded-none border-x-0 border-b border-t-0 border-rolex-brown bg-transparent font-bold shadow-none focus:border-rolex-brown focus:outline-none focus-visible:ring-0"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-rolex-16 text-left font-bold">Phone Number (optional)</FormLabel>
                      <FormControl>
                        <Input
                          className="my-0 rounded-none border-x-0 border-b border-t-0 border-rolex-brown bg-transparent font-bold shadow-none focus:border-rolex-brown focus:outline-none focus-visible:ring-0"
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-rolex-16 text-left font-bold">Country</FormLabel>
                      <FormControl>
                        <Input
                          className="my-0 rounded-none border-x-0 border-b border-t-0 border-rolex-brown bg-transparent font-bold shadow-none focus:border-rolex-brown focus:outline-none focus-visible:ring-0"
                          disabled
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-rolex-16 text-left font-bold">Store City</FormLabel>
                      <FormControl>
                        <Input
                          className="my-0 rounded-none border-x-0 border-b border-t-0 border-rolex-brown bg-transparent font-bold shadow-none focus:border-rolex-brown focus:outline-none focus-visible:ring-0"
                          disabled
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="storeAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-rolex-16 text-left font-bold">Store Address</FormLabel>
                      <FormControl>
                        <Input
                          className="my-0 rounded-none border-x-0 border-b border-t-0 border-rolex-brown bg-transparent font-bold shadow-none focus:border-rolex-brown focus:outline-none focus-visible:ring-0"
                          disabled
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="acceptTerms"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} className="p-2.5" />
                      </FormControl>
                      <FormLabel className="text-rolex-16 text-left font-bold">
                        * I have read and accepted the terms and conditions and privacy policy
                      </FormLabel>
                    </FormItem>
                  )}
                />

                <div className="flex justify-center gap-4">
                  <button type="button" onClick={() => setStep(1)} className="btn rolex-primary">
                    Back
                  </button>
                  <button type="submit" disabled={isSubmitting} className="btn rolex-primary disabled:opacity-50">
                    {isSubmitting ? 'Sending...' : 'Send'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center">
                <h1 className="mb-4 text-[50px] font-bold">Thank You</h1>
                <p className="body20-light mb-4 font-bold">
                  Your message has been successfully sent to the Rolex team at Charles Fox.
                </p>
                <p className="body20">
                  One of our Rolex sales advisors will be reviewing your request and responding as soon as possible.
                </p>
                <Image
                  src="/images/rolex-contact-thank-you.webp"
                  alt="Rolex Contact Thank You"
                  className="mt-4"
                  width={500}
                  height={300}
                />
              </div>
            )}
          </form>
        </Form>
      </div>
    </div>
  );
}
