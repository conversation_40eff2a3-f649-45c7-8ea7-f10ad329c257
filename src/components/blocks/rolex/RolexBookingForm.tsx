'use client';

import { BookingFormData, submitBooking } from '@/actions/booking';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { capitalize, cn } from '@/lib/utils';
import { format, isPast, isSunday } from 'date-fns';
import { Check, ChevronsUpDown, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useBooking } from './RolexBookingSystem';
import RolexCardGroup from './RolexCardGroup';

type BankHoliday = {
  title: string;
  date: string;
  notes: string;
  bunting: boolean;
};

type BankHolidaysData = {
  'england-and-wales': {
    division: string;
    events: BankHoliday[];
  };
  scotland: {
    division: string;
    events: BankHoliday[];
  };
  'northern-ireland': {
    division: string;
    events: BankHoliday[];
  };
};

type FormStep = 'intro' | 'time' | 'details' | 'confirmed';

const FormHeader = ({ small, main }: { small: string; main: string }) => (
  <div className="flex flex-col items-center">
    <span className="body24-bold text-rolex-brown">{small}</span>
    <h1 className="headline50 mt-2 text-center">{main}</h1>
  </div>
);

// Common input style class for consistency
const inputStyles =
  'font-rolex border-0 border-b-[1px] rounded-none border-rolex-brown bg-transparent text-sm text-rolex-brown placeholder:text-rolex-brown';

// List of countries for the combobox
const countries = [
  { value: 'uk', label: 'United Kingdom' },
  { value: 'us', label: 'United States' },
  { value: 'ca', label: 'Canada' },
  { value: 'au', label: 'Australia' },
  { value: 'fr', label: 'France' },
  { value: 'de', label: 'Germany' },
  { value: 'it', label: 'Italy' },
  { value: 'es', label: 'Spain' },
  { value: 'jp', label: 'Japan' },
  { value: 'ch', label: 'Switzerland' },
  { value: 'cn', label: 'China' },
  { value: 'in', label: 'India' },
  { value: 'br', label: 'Brazil' },
  { value: 'ru', label: 'Russia' },
  { value: 'ae', label: 'United Arab Emirates' },
  { value: 'sg', label: 'Singapore' },
  { value: 'hk', label: 'Hong Kong' },
];

export default function RolexBookingForm({ cards }: any) {
  const [formStep, setFormStep] = useState<FormStep>('intro');
  const [date, setDate] = useState<Date | undefined>();
  const [hour, setHour] = useState<string>('');
  const [minute, setMinute] = useState<string>('');
  const [title, setTitle] = useState<string>('');
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [phone, setPhone] = useState<string>('');
  const [country, setCountry] = useState('');
  const [openCountry, setOpenCountry] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);
  const [bankHolidays, setBankHolidays] = useState<string[]>([]);
  const bookingState = useBooking();

  // Fetch bank holidays when component mounts
  useEffect(() => {
    const fetchBankHolidays = async () => {
      try {
        const response = await fetch('https://www.gov.uk/bank-holidays.json');
        const data: BankHolidaysData = await response.json();
        const holidays = data['england-and-wales'].events.map((event) => event.date);
        setBankHolidays(holidays);
      } catch (error) {
        console.error('Failed to fetch bank holidays:', error);
        setSubmissionError('Could not load bank holiday data. Using fallback validation.');
      }
    };
    fetchBankHolidays();
  }, []);

  // Update isDateDisabled to use fetched bank holidays
  const isDateDisabled = (date: Date) => {
    const formattedDate = format(date, 'yyyy-MM-dd');
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return (
      isSunday(date) || // Disable Sundays
      bankHolidays.includes(formattedDate) || // Disable bank holidays
      isPast(date) || // Disable past dates
      date < today // Ensure no dates before today
    );
  };

  // Update handleSubmit with dynamic validation
  const handleSubmit = async () => {
    if (!date || !hour || !minute || !title || !firstName || !lastName || !email || !phone || !country) {
      setSubmissionError('Please fill in all required fields');
      return;
    }

    if (isDateDisabled(date)) {
      setSubmissionError('Selected date is not available for booking');
      return;
    }

    setIsSubmitting(true);
    setSubmissionError(null);

    try {
      const formattedTime = `${hour}:${minute}`;
      const formattedDate = format(date, 'yyyy-MM-dd');

      const formData: BookingFormData = {
        title,
        firstName,
        lastName,
        email,
        phone,
        country,
        date: formattedDate,
        time: formattedTime,
        type: bookingState.formPath,
      };

      const result = await submitBooking(formData);

      if (result.success) {
        setFormStep('confirmed');
      } else {
        setSubmissionError(result.message || 'Failed to submit booking');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmissionError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (bookingState.formPath === 'initial') {
    return (
      <div className="rolex-text-24 mx-auto w-full bg-rolex-beige-400 px-[8%] pb-0 pt-[40px] text-center text-rolex-brown">
        <FormHeader small="Book an appointment" main="Appointment purpose" />
        <p className="body20-light mx-auto mb-[30px] max-w-[58%]">
          We offer you an in-store experience with one of our Rolex sales advisors who will share their expertise to
          guide you and answer all your questions.
        </p>
        <p className="body20-bold pb-[50px]">Please select the purpose of your appointment</p>
        <RolexCardGroup cards={cards} uiType="booking" isBooking />
      </div>
    );
  }

  // Intro Section
  const renderIntro = () => (
    <div className="mx-auto text-center">
      <FormHeader small="Book an appointment" main="Please note" />
      <p className="body20-light mx-auto max-w-md">
        {bookingState.formPath === 'discover'
          ? 'Kindly note that the availability of certain Rolex watches in our boutique may be limited.'
          : 'Please note that you may visit our store directly to bring/collect your watch to/from our after-sales service.'}
      </p>
      <div className="flex justify-center pt-[60px]">
        <Button className="rolex-primary rounded-full" onClick={() => setFormStep('time')}>
          I understand
        </Button>
      </div>
    </div>
  );

  // Time & Date Section
  const renderTime = () => {
    // Define available minutes based on selected hour
    const availableMinutes =
      hour === '09'
        ? [
            { value: '30', label: ':30' },
            { value: '45', label: ':45' },
          ]
        : hour === '15'
          ? [
              { value: '00', label: ':00' },
              { value: '15', label: ':15' },
              { value: '30', label: ':30' },
            ]
          : [
              { value: '00', label: ':00' },
              { value: '15', label: ':15' },
              { value: '30', label: ':30' },
              { value: '45', label: ':45' },
            ];

    return (
      <div className="mx-auto max-w-screen-md space-y-6">
        <FormHeader small="Book an appointment" main="Please choose a date and time" />
        <div className="space-y-4">
          <div>
            <Label htmlFor="date">Preferred Date</Label>
            <Popover>
              <PopoverTrigger className="mx-0 w-full bg-transparent px-0">
                <Input
                  id="date"
                  className={inputStyles}
                  placeholder="Select a date"
                  readOnly
                  value={date ? date.toLocaleDateString() : ''}
                />
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" side="bottom">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(newDate) => {
                    if (newDate && !isDateDisabled(newDate)) {
                      setDate(newDate);
                    }
                  }}
                  disabled={isDateDisabled}
                  fromDate={new Date()}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="hour">Hour</Label>
              <Select value={hour} onValueChange={setHour}>
                <SelectTrigger id="hour" className={inputStyles}>
                  <SelectValue placeholder="Hour" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="09">9 AM</SelectItem>
                  <SelectItem value="10">10 AM</SelectItem>
                  <SelectItem value="11">11 AM</SelectItem>
                  <SelectItem value="12">12 PM</SelectItem>
                  <SelectItem value="13">1 PM</SelectItem>
                  <SelectItem value="14">2 PM</SelectItem>
                  <SelectItem value="15">3 PM</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <Label htmlFor="minute">Minute</Label>
              <Select value={minute} onValueChange={setMinute}>
                <SelectTrigger id="minute" className={inputStyles}>
                  <SelectValue placeholder="Minute" />
                </SelectTrigger>
                <SelectContent>
                  {availableMinutes.map((min) => (
                    <SelectItem key={min.value} value={min.value}>
                      {min.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <div className="flex justify-center">
          <Button
            className="rolex-primary rounded-full"
            onClick={() => setFormStep('details')}
            disabled={!date || !hour || !minute}
          >
            Next
          </Button>
        </div>
      </div>
    );
  };

  // User Details Section
  const renderDetails = () => (
    <div className="mx-auto w-full max-w-screen-md space-y-6">
      <FormHeader small="Book an appointment" main="Your details" />
      <div className="space-y-4">
        <div className="flex gap-4">
          <div className="w-1/4">
            <Label htmlFor="title">Title</Label>
            <Select value={title} onValueChange={setTitle}>
              <SelectTrigger id="title" className={inputStyles}>
                <SelectValue placeholder="Title" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Mr">Mr.</SelectItem>
                <SelectItem value="Mrs">Mrs.</SelectItem>
                <SelectItem value="Miss">Miss.</SelectItem>
                <SelectItem value="Ms">Ms.</SelectItem>
                <SelectItem value="Dr">Dr.</SelectItem>
                <SelectItem value="Prof">Prof.</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-3/4">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              className={inputStyles}
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
            />
          </div>
        </div>
        <div>
          <Label htmlFor="lastName">Last Name</Label>
          <Input id="lastName" className={inputStyles} value={lastName} onChange={(e) => setLastName(e.target.value)} />
        </div>
        <div>
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            className={inputStyles}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
        </div>
        <div>
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            type="tel"
            className={inputStyles}
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
          />
        </div>
        <div>
          <Label htmlFor="country">Country</Label>
          <Popover open={openCountry} onOpenChange={setOpenCountry}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={openCountry}
                className={cn(
                  'mx-auto w-full justify-between',
                  inputStyles,
                  'hover:bg-transparent hover:text-rolex-brown'
                )}
              >
                {country ? countries.find((c) => c.value === country)?.label : 'Select country'}
                <ChevronsUpDown className="ml-2 size-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
              <Command>
                <CommandInput placeholder="Search country..." />
                <CommandList>
                  <CommandEmpty>No country found.</CommandEmpty>
                  <CommandGroup className="max-h-60 overflow-y-auto">
                    {countries.map((c) => (
                      <CommandItem
                        key={c.value}
                        value={c.label}
                        onSelect={() => {
                          setCountry(country === c.value ? '' : c.value);
                          setOpenCountry(false);
                        }}
                      >
                        <Check className={cn('mr-2 h-4 w-4', country === c.value ? 'opacity-100' : 'opacity-0')} />
                        {c.label}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
        {submissionError && <div className="text-sm text-red-500">{submissionError}</div>}
      </div>
      <div className="flex justify-center">
        <Button className="rolex-primary rounded-full" onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 size-4 animate-spin" />
              Submitting...
            </>
          ) : (
            'Submit'
          )}
        </Button>
      </div>
    </div>
  );

  // Submission Confirmed Section
  const renderConfirmed = () => {
    // Format date for iCalendar (UTC format required)
    const getICalDateTime = (date: Date, hour: string, minute: string) => {
      const dt = new Date(date);
      dt.setHours(parseInt(hour), parseInt(minute), 0, 0);
      return format(dt, "yyyyMMdd'T'HHmmss'Z'");
    };

    const startDateTime = date && hour && minute ? getICalDateTime(date, hour, minute) : '';
    const endDateTime = date && hour && minute ? getICalDateTime(date, hour, String(parseInt(minute) + 60)) : '';

    // Generate iCalendar content
    const iCalContent = `
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//xAI//Rolex Booking//EN
CALSCALE:GREGORIAN
BEGIN:VEVENT
UID:${crypto.randomUUID()}@rolex.charlesfox.com
DTSTAMP:${format(new Date(), "yyyyMMdd'T'HHmmss'Z'")}
DTSTART:${startDateTime}
DTEND:${endDateTime}
SUMMARY:Rolex Appointment at Charles Fox
DESCRIPTION:Your Rolex appointment at Charles Fox - Purpose: ${capitalize(bookingState.formPath)}
LOCATION:21 - 22 The Arcade, Bournemouth, Dorset, BH1 2AH, United Kingdom
STATUS:CONFIRMED
SEQUENCE:0
BEGIN:VALARM
TRIGGER:-PT15M
ACTION:DISPLAY
DESCRIPTION:Reminder: Rolex Appointment
END:VALARM
END:VEVENT
END:VCALENDAR
`.trim();

    return (
      <div className="mx-auto max-w-screen-lg space-y-6 text-center">
        <FormHeader small="Book an appointment" main="Your appointment is confirmed" />
        <span className="font-bold">We are pleased to confirm your appointment at Charles Fox</span>
        <p className="mx-auto max-w-md">
          Thank you. Please introduce yourself at the entrance upon arrival. A Rolex sales advisor will be available to
          welcome you.
        </p>
        <div className="mx-auto flex w-96 flex-col items-start space-y-3 bg-white px-4 py-6 text-left">
          <h2 className="mb-4 text-xl font-bold">Your appointment details</h2>
          <div className="space-y-8">
            <div className="flex flex-col space-y-2">
              <span className="font-bold">Purpose</span>
              <span>{capitalize(bookingState.formPath)}</span>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="font-bold">Date</span>
              <span>
                {date && hour && minute
                  ? format(
                      new Date(date.getFullYear(), date.getMonth(), date.getDate(), parseInt(hour), parseInt(minute)),
                      'PPPPp'
                    )
                  : 'TBD'}
              </span>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="font-bold">Location</span>
              <span>
                21 - 22 The Arcade <br /> Bournemouth <br /> Dorset <br /> BH1 2AH
              </span>
            </div>
          </div>
        </div>
        <div className="mx-auto flex w-96 flex-col items-start space-y-4">
          <Link
            href={`data:text/calendar;charset=utf-8,${encodeURIComponent(iCalContent)}`}
            download="rolex-appointment.ics"
            className="text-rolex-green-500 hover:underline"
          >
            Add to calendar
          </Link>
          <Link href="/rolex" className="btn rolex-primary mx-auto self-center rounded-full">
            Go back to homepage
          </Link>
        </div>
      </div>
    );
  };

  return (
    <div className="rolex-text-24 mx-auto grid w-full place-items-center space-y-4 bg-white px-4 py-16 text-rolex-brown md:min-h-[646px]">
      {formStep === 'intro' && renderIntro()}
      {formStep === 'time' && renderTime()}
      {formStep === 'details' && renderDetails()}
      {formStep === 'confirmed' && renderConfirmed()}
    </div>
  );
}
