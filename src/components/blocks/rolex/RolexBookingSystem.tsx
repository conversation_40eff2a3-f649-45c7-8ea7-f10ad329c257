'use client';

import { createContext, ReactNode, useContext, useState } from 'react';
import RolexBookingForm from './RolexBookingForm';

type BookingPath = 'initial' | 'discover' | 'service';

interface BookingContextType {
  formPath: BookingPath;
  setFormPath: (step: BookingPath) => void;
}

const BookingContext = createContext<BookingContextType | undefined>(undefined);

export function RolexBookingSystem({ children }: { children: ReactNode }) {
  const [formPath, setFormPath] = useState<BookingPath>('initial');

  const contextValue = {
    formPath: formPath,
    setFormPath: (step: BookingPath) => setFormPath(step),
  };

  return <BookingContext.Provider value={contextValue}>{children}</BookingContext.Provider>;
}

/**
 * Hook to access booking context
 * @returns Booking context values and methods
 * @throws Error if used outside RolexBookingSystem
 */
export function useBooking() {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBooking must be used within a RolexBookingSystem');
  }
  return context;
}

export default function BookPage({ cards }: any) {
  return <RolexBookingForm cards={cards} />;
}
