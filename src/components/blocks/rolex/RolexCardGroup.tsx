'use client';

import { cn } from '@/lib/utils';
import { SanityImageSource } from '@sanity/asset-utils';
import RolexCard, { RolexCardProps } from './RolexCard';
import * as Type from '@/sanity/types';

export default function RolexCardGroup({
  title,
  cards,
  uiType,
  isBooking,
}: {
  title?: string;
  cards: RolexCardProps[];
  uiType: Type.RolexWatchGroup['uiType'];
  isBooking?: boolean;
}) {
  // 计算网格布局类名
  const getGridClasses = () => {
    // 新闻
    if (uiType == 'post') {
      return 'grid grid-cols-1 md:grid-cols-2 gap-2 gap-y-[20px] sm:gap-y-[40px]';
    }

    // 列表
    if (uiType == 'seriesWatch') {
      return 'grid grid-cols-1 md:grid-cols-3 gap-4';
    }

    // 默认网格布局
    const baseGridClasses = 'grid w-full gap-4';

    // 如果只有2张卡片或者是新手表，使用2列布局
    if (uiType == 'newWatches' || uiType == 'twoColumns' || uiType == 'booking') {
      return `${baseGridClasses} grid-cols-1 gap-2 gap-y-4 sm:gap-y-[90px] md:grid-cols-2 pb-[90px]`;
    }

    // 否则使用3列布局
    return `${baseGridClasses} grid-cols-1 md:grid-cols-3 gap-y-6 sm:gap-y-[30px]`;
  };

  // 计算容器的样式类名
  const getContainerClasses = () => {
    if (uiType === 'twoColumns' || uiType === 'multiColumns') {
      return 'px-[8%]';
    }

    return `px-[8%]`;
  };

  return (
    <div className={cn('mx-auto w-full pb-[60px] md:pb-[90px] pt-0', getContainerClasses())}>
      {title && <h2 className="headline36">{title}</h2>}
      <div className={cn('w-full', getGridClasses())}>
        {cards.map(({ image, mobileImage, title, ctaSubheading, ctaHeading, ctaText, ctaLink, description }, index) => (
          <RolexCard
            key={index}
            image={image as SanityImageSource}
            mobileImage={mobileImage as SanityImageSource}
            title={title as string}
            ctaSubheading={ctaSubheading as string}
            ctaHeading={ctaHeading as string}
            ctaText={ctaText as string}
            ctaLink={ctaLink as string}
            uiType={uiType}
            description={description as string}
            className="w-full"
            isBooking={isBooking}
          />
        ))}
      </div>
    </div>
  );
}
