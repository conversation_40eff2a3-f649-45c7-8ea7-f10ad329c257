import { cn } from '@/lib/utils';
import { PortableText } from '@portabletext/react';
import type { PortableTextBlock } from '@portabletext/types';
import Image from 'next/image';
import Link from 'next/link';
import { ReactNode } from 'react';

export interface ImageContentProps {
  title: string;
  content?: PortableTextBlock[];
  dateObjects?: {
    year: number;
    description: string;
  }[];
  imageUrl: string;
  imageAlt: string;
  imagePosition?: 'left' | 'right';
}

const components = {
  marks: {
    link: ({ value, children }: { value?: { href: string }; children: ReactNode }) => {
      return (
        <Link
          href={value?.href ?? '/'}
          className="transition-colors duration-300 hover:text-foreground-500"
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </Link>
      );
    },
  },
  types: {
    image: ({ value }: { value?: { src: string; alt: string } }) => {
      return (
        <Image
          src={value?.src ?? '/default.jpg'}
          alt={value?.alt ?? 'Image'}
          width={700}
          height={475}
          className="object-cover"
        />
      );
    },
  },
};

export default function ImageContent({
  title,
  content,
  dateObjects,
  imageUrl,
  imageAlt,
  imagePosition = 'right',
}: ImageContentProps) {
  const imageLeft = imagePosition === 'left';
  const imageRight = imagePosition === 'right';

  return (
    <div className={cn('flex flex-col md:gap-12 py-8 md:flex-row md:items-center', dateObjects && 'md:gap-0')}>
      <div className={cn('order-2 flex-1', imageLeft && 'md:order-2', imageRight && 'md:order-1')}>
        <h2 className="mb-4 mt-0 font-bold">{title}</h2>
        <div className={cn('space-y-4', dateObjects && 'space-y-8')}>
          {content && <PortableText value={content} components={components} />}
          {dateObjects &&
            dateObjects.map(({ year, description }, index) => (
              <div key={index} className="flex flex-col items-center space-y-2">
                <div className="relative font-heading first:text-3xl">
                  {year === 9999 ? (
                    'Today'
                  ) : (
                    <div className={cn('flex items-start', index === 0 && '-space-x-1')}>
                      <span className={cn('align-text-bottom text-3xl', index === 0 && 'text-7xl')}>
                        {year.toString()[0]}
                      </span>
                      <span className={cn('text-3xl', index === 0 && 'text-6xl')}>{year.toString().slice(1, 3)}</span>
                      <span className={cn('align-text-bottom text-3xl', index === 0 && 'text-7xl')}>
                        {year.toString()[3]}
                      </span>
                    </div>
                  )}
                  <div
                    className={cn(
                      'absolute -bottom-1 left-1/2 -translate-x-1/2 h-0.5 w-3/5 bg-accent',
                      index === 0 && 'bottom-2.5 w-2/5 -translate-x-[60%]'
                    )}
                  />
                </div>
                <p className="max-w-72 text-center">{description}</p>
              </div>
            ))}
        </div>
      </div>
      <div className={cn('order-1 flex-1', imageLeft && 'md:order-1', imageRight && 'md:order-2')}>
        <Image
          src={imageUrl}
          alt={imageAlt}
          width={600}
          height={400}
          className="w-full rounded-lg object-cover shadow-md"
        />
      </div>
    </div>
  );
}
