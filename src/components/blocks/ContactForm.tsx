'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Mail, MapPin, Phone } from 'lucide-react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { sendEmail } from '@/app/actions/sendEmail';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { OpeningTimes } from '@/lib/utils';
import { getContactInformation } from '@/sanity/lib/helpers';
import { ContactInformationQueryResult } from '@/sanity/types';
import { useEffect, useState } from 'react';
import CollapsibleOpeningTimes from '../general/OpeningTimes';

const formSchema = z.object({
  name: z.string().min(2, {
    message: 'Name must be at least 2 characters.',
  }),
  email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  subject: z.string().min(1, {
    message: 'Subject is required.',
  }),
  message: z.string().min(10, {
    message: 'Message must be at least 10 characters.',
  }),
  terms: z.boolean().refine((val) => val, {
    message: 'You must accept the terms and conditions.',
  }),
  phoneNumber: z.string().optional(),
});

export interface ContactFormProps {
  title: string;
  subtitle: string;
  contactInfo: {
    phone: string;
    email: string;
    openingTimes: OpeningTimes;
    address: string;
  };
}

export function ContactForm({ title, subtitle }: Partial<ContactFormProps>) {
  const [data, setData] = useState<Partial<ContactInformationQueryResult> | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const contactData = await getContactInformation();
        setData(contactData);
      } catch (error) {
        throw error;
      }
    };
    fetchData();
  }, [data]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      subject: '',
      message: '',
      terms: false,
      phoneNumber: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    try {
      const response = await sendEmail(values);
      if (response.success) {
        setIsSubmitted(true);
      } else {
        throw new Error('Failed to send email');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      setIsSubmitted(false);
      alert('There was an error sending your message. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="px-4">
      <Card className="mx-auto my-8 max-w-screen-xl">
        <CardTitle>
          <h1 className="py-8 text-center">{title}</h1>
        </CardTitle>
        <CardContent>
          <div className="mx-auto max-w-4xl py-12 sm:px-6 lg:px-8">
            <div className="grid gap-12 md:grid-cols-2">
              <div className="mx-auto space-y-6 md:mx-0">
                {/* Contact Information */}
                <div>
                  <h2 className="mb-4 font-bold">Contact Information</h2>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <Phone className="mr-2 size-5" />
                      <Link
                        href={`tel:${data?.contactInfo?.phone}`}
                        className="transition-colors duration-300 hover:text-foreground-500"
                      >
                        {data?.contactInfo?.phone as string}
                      </Link>
                    </div>
                    <div className="flex items-center">
                      <Mail className="mr-2 size-5" />
                      <Link
                        href={`mailto:${data?.contactInfo?.email}`}
                        className=" transition-colors duration-300 hover:text-foreground-500"
                      >
                        {data?.contactInfo?.email as string}
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Opening Times */}
                <div className="flex flex-col space-y-4">
                  <CollapsibleOpeningTimes openingTimes={data?.contactInfo?.openingTimes ?? {}} isCharlesFox />
                </div>

                {/* Address */}
                <div>
                  <h2 className=" mb-4 font-bold">Address</h2>
                  <div className="flex items-start">
                    <MapPin className="mr-2 mt-1 size-5" />
                    <div className="flex flex-col">
                      <address className=" whitespace-pre-line not-italic">{data?.contactInfo?.address}</address>
                      <Link href="https://maps.google.com" target="_blank" className="text-blue-600 hover:underline">
                        View on Google Maps
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                {isSubmitted ? (
                  <div className="text-center">
                    <h2 className="text-2xl font-bold">
                      Thank you for getting in touch. You will hear back from us soon.
                    </h2>
                  </div>
                ) : (
                  <div>
                    <h2 className=" mb-4 font-bold">Send us a message</h2>
                    <p className=" mb-6">{subtitle}</p>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Your name <span className="text-red-500">*</span>
                              </FormLabel>
                              <FormControl>
                                <Input placeholder="John Doe" required {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Your e-mail address <span className="text-red-500">*</span>
                              </FormLabel>
                              <FormControl>
                                <Input placeholder="<EMAIL>" required {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="subject"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Subject <span className="text-red-500">*</span>
                              </FormLabel>
                              <FormControl>
                                <Input required {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="phoneNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Phone Number</FormLabel>
                              <FormControl>
                                <Input placeholder="+44 1234 567890" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="message"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Your message <span className="text-red-500">*</span>
                              </FormLabel>
                              <FormControl>
                                <Textarea required placeholder="Type your message here." {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="terms"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                              <FormControl>
                                <div className="flex h-6 items-center justify-center">
                                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                                </div>
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>
                                  I have read and accepted the{' '}
                                  <Link
                                    href="/privacy-policy"
                                    className="underline transition-colors duration-300 hover:text-foreground-500"
                                  >
                                    Privacy Policy
                                  </Link>
                                  {'  '}
                                  and{'  '}
                                  <Link
                                    href="/cookie-policy"
                                    className="underline transition-colors duration-300 hover:text-foreground-500"
                                  >
                                    Cookie Policy
                                  </Link>
                                </FormLabel>
                                <FormMessage />
                              </div>
                            </FormItem>
                          )}
                        />
                        <Button type="submit" className="primary w-full" variant="outline" disabled={isLoading}>
                          {isLoading ? 'Sending...' : 'Send'}
                        </Button>
                      </form>
                    </Form>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
