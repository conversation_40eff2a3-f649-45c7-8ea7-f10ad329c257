import { Fragment } from 'react';

interface FinancialPreAppraisalProps {
  subtitle: string;
  retailPrices: number[];
}

interface PriceBlock {
  price: number;
  deposit: number;
  installments: { months: number; amount: number }[];
}

function calculatePriceBlock(price: number): PriceBlock {
  const deposit = price * 0.2;
  const remainingAmount = price - deposit;
  return {
    price,
    deposit,
    installments: [12, 24, 36].map((months) => ({
      months,
      amount: Number((remainingAmount / months).toFixed(2)),
    })),
  };
}

export default function FinancialPreAppraisal({ subtitle, retailPrices }: FinancialPreAppraisalProps) {
  const priceBlocks = retailPrices.map(calculatePriceBlock);

  return (
    <section
      id="appraisal"
      className="not-prose mx-auto mb-4 w-full max-w-screen-3xl rounded-lg border border-foreground-500 bg-white p-8"
    >
      <h1 className="mb-2 text-2xl font-bold text-foreground-600 md:text-4xl">Financial Pre-Appraisal</h1>

      <p className="mb-12 max-w-4xl text-lg">{subtitle}</p>

      <div className="grid gap-8 md:grid-cols-2">
        {priceBlocks.map((block, index) => (
          <div key={index} className="space-y-2">
            <h2 className="text-2xl font-light text-accent">Retail Price £{block.price.toLocaleString()}</h2>

            <div className="grid grid-cols-2 gap-x-8 gap-y-2">
              <b className="">Deposit Required 20%</b>
              <div className="font-medium">£{block.deposit.toFixed(1)}</div>

              <b className="col-span-1 mt-4">No of Payments</b>
              <b className="col-span-1 mt-4">Monthly Installments</b>

              {block.installments.map((installment, i) => (
                <Fragment key={i}>
                  <span>{installment.months}</span>
                  <span className="font-medium">£{installment.amount.toFixed(2)}</span>
                </Fragment>
              ))}

              <span>Interest Rate</span>
              <span className="font-medium">0%</span>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
