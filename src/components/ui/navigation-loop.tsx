'use client';

import { adjustYearIfBeforeApril, cn } from '@/lib/utils';
import { usePathname } from 'next/navigation';
import * as React from 'react';
import { NavigationMenuItem, NavigationMenuLink } from './navigation-menu';

const generateHref = (item: string, isRolex: boolean): string => {
  const lowercaseItem = item.toLowerCase().replace(/\s+/g, '-');

  if (isRolex && item.includes('Discover')) {
    return '/rolex';
  }

  if (isRolex && item == 'Rolex Watches') {
    return '/rolex/watches';
  }

  if (isRolex && item === `New Watches ${adjustYearIfBeforeApril(new Date())}`) {
    return '/rolex/new-watches';
  }

  if (isRolex) {
    return `/rolex/${lowercaseItem}`;
  }

  return `/${lowercaseItem}`;
};

const isActiveRoute = (href: string, currentRoute?: string): boolean => {
  if (!currentRoute) return false;

  if (href === '/rolex' && href !== currentRoute) {
    return false;
  }

  return currentRoute.includes(href);
};

const NavigationLoop: React.FC<{
  items: string[];
  currentRoute?: string;
  isRolex?: boolean;
}> = ({ items, isRolex }) => {
  const pathname = usePathname();

  return items.map((item: string) => {
    const href = generateHref(item, isRolex ?? false);
    const isActive = isActiveRoute(href, pathname ?? undefined);

    return (
      <NavigationMenuItem key={item}>
        <NavigationMenuLink
          href={href}
          className={cn(
            'text-foreground-500 transition-colors duration-300 hover:text-foreground-500/60',
            isRolex && 'font-bold text-[15px] md:px-[.5rem] px-2 text-white leading-[1.2] hover:text-rolex-green-400',
            isActive && isRolex && 'text-rolex-green-400'
          )}
        >
          {item}
        </NavigationMenuLink>
      </NavigationMenuItem>
    );
  });
};

export { NavigationLoop };
