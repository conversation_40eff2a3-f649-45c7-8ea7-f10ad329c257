import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { SettingsQueryResult } from '@/sanity/types';
import Image from 'next/image';
import Link from 'next/link';
import MobileNavigation from './MobileNavigation';
import Navigation from './Navigation';
import RolexClock from './rolex/RolexClock';

export interface HeaderContactProps {
  phone: string;
  email: string;
}
export const ContactInformation = ({ phone, email }: HeaderContactProps) => (
  <div className="flex flex-col space-y-0 text-foreground-500 transition-colors ">
    <a href={`tel:${phone}`} className="transition-colors duration-300 hover:text-foreground-500">
      {phone}
    </a>
    <a href={`mailto:${email}`} className="transition-colors duration-300 hover:text-foreground-500">
      {email}
    </a>
  </div>
);

export default function Header({
  logo,
  phone,
  email,
}: {
  logo: NonNullable<SettingsQueryResult>['brandLogo'];
  phone: HeaderContactProps['phone'];
  email: HeaderContactProps['email'];
}) {
  return (
    <Card className="z-50 shadow-none">
      <CardHeader className="relative z-50 mx-auto flex max-w-full flex-col items-center bg-white py-3 sm:py-12 md:grid md:grid-cols-3 md:px-24">
        <div className="hidden flex-col text-center md:flex md:justify-self-start">
          <ContactInformation phone={phone} email={email} />
        </div>
        <div className="absolute right-4 top-3 z-50 sm:top-12 md:hidden">
          <MobileNavigation phone={phone} email={email} />
        </div>
        <Link href="/" className="z-[900] flex cursor-pointer justify-center">
          <Image
            src={logo.url as string}
            width={logo.width as number}
            height={logo.height as number}
            alt="Charles Fox Logo"
            className="w-52 md:w-auto"
          />
        </Link>
        <div className="hidden justify-end md:flex">
          <RolexClock />
        </div>
      </CardHeader>
      <CardContent className="bg-rolex-beige-400 py-0 md:py-px md:pb-3">
        <Navigation />
      </CardContent>
    </Card>
  );
}
