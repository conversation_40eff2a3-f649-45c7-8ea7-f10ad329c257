'use client';

import { Button } from '@/components/ui/button';
import { useCookiePopup } from '@/contexts/CookiePopupContext';
import Image from 'next/image';
import { useEffect, useState } from 'react';

type CookiePopupProps = {
  logo: {
    url: string | null;
    width: number | null;
    height: number | null;
  };
};

const DEFAULT_COOKIE_SETTINGS = {
  necessary: true,
  rolex: false,
};

const CookiePopup: React.FC<CookiePopupProps> = ({ logo }) => {
  const { isOpen, openPopup, closePopup } = useCookiePopup();
  const [cookies, setCookies] = useState(DEFAULT_COOKIE_SETTINGS);

  useEffect(() => {
    // Check for cookie consent in cookies instead of localStorage
    const consent = document.cookie.split('; ').find((row) => row.startsWith('cookieConsent='));

    // Set rlx-consent cookie instead of localStorage
    // document.cookie = 'rlx-consent=false; path=/; max-age=31536000'; // 1 year expiration

    if (!consent) {
      openPopup();
    } else {
      // Parse the cookie value if it exists
      try {
        const consentValue = consent.split('=')[1];
        if (consentValue) {
          setCookies(JSON.parse(decodeURIComponent(consentValue)));
        }
      } catch (e) {
        console.error(e);
        openPopup();
      }
    }
  }, [openPopup]);

  const handleCheckboxChange = (key: keyof typeof cookies) => {
    setCookies((prevState) => ({
      ...prevState,
      [key]: !prevState[key],
    }));
  };

  type ModalSelectionOption = 'A' | 'N' | 'S';

  const handleSelectionChange = (selection: ModalSelectionOption) => {
    const updatedCookies = { ...cookies };
    if (selection === 'A') {
      document.cookie = 'rlx-consent=true; path=/; max-age=31536000'; // 1 year expiration
      updatedCookies.rolex = true;
    } else if (selection === 'N') {
      document.cookie = 'rlx-consent=false; path=/; max-age=31536000';
      updatedCookies.rolex = false;
    }

    // Store cookie consent in cookies instead of localStorage
    document.cookie = `cookieConsent=${encodeURIComponent(JSON.stringify(updatedCookies))}; path=/; max-age=31536000`; // 1 year expiration

    setCookies(updatedCookies);
    closePopup();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[1000] flex items-center justify-center bg-black/50">
      <div className="m-2 flex max-h-[90vh] w-full flex-col items-center justify-start overflow-y-auto rounded-lg bg-white p-4 shadow-lg sm:max-w-sm sm:p-6 md:max-w-2xl">
        {logo?.url && (
          <Image
            src={logo.url}
            width={logo.width ?? undefined}
            height={logo.height ?? undefined}
            alt="Logo"
            className="mb-4 h-auto"
          />
        )}
        <div className="flex flex-col items-center p-4">
          <p className="mb-4 text-center text-lg font-semibold">
            <b>We value your privacy</b>
          </p>
          <p className="mb-4 text-center text-gray-700">
            On our website, we use services (including from third-party providers) that help us to improve our online
            presence. The following categories of cookies are used by us and can be managed in the cookie settings.
          </p>
          <p className="mb-4 text-center text-gray-700">
            We need your consent before being able to use these services. Alternatively, you may click to refuse to
            consent, or access more detailed information and change your preferences before consenting. Your preferences
            will apply to this website only. You can change your preferences at any time by returning to this site or
            visiting our settings.
          </p>
          <p className="mb-4 text-center text-gray-700">
            By authorising third-party services, you allow the placement and reading of cookies and the use of tracking
            technologies required to keep our website reliable and secure.
          </p>
        </div>

        <div className="flex w-full flex-col p-4">
          <h2 className="mb-2 text-lg font-semibold">Strictly Necessary</h2>
          <div className="flex flex-row items-center">
            <p className="flex-1 text-gray-700">
              These cookies are required for the basic functionality of the website. These cookies are exempt from the
              need for informed consent under the ePrivacy regulations.
            </p>
            <input
              type="checkbox"
              checked={cookies.necessary}
              disabled
              className="form-checkbox ml-4 size-4 cursor-not-allowed text-blue-600"
            />
          </div>
        </div>

        <div className="flex w-full flex-col p-4">
          <h2 className="mb-2 text-lg font-semibold">Rolex (Adobe Analytics and Content Square)</h2>
          <div className="flex flex-row items-center">
            <p className="flex-1 text-gray-700">
              Privacy policy{' '}
              <a
                href="https://www.rolex.com/legal-notices/cookies.html"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600"
              >
                here
              </a>
              <br />
              Purposes (consent)
              <br />
              Legitimate Interest Purpose(s)
            </p>
            <input
              type="checkbox"
              checked={cookies.rolex}
              onChange={() => handleCheckboxChange('rolex')}
              className="form-checkbox size-4 text-blue-600"
            />
          </div>
        </div>

        <div className="flex w-full flex-col justify-center space-y-4 py-4 sm:flex-row sm:justify-around sm:space-x-4 sm:space-y-0">
          <Button onClick={() => handleSelectionChange('S')} className="primary w-full sm:w-auto" variant="outline">
            Save Preferences
          </Button>
          <Button onClick={() => handleSelectionChange('A')} className="primary w-full sm:w-auto" variant="outline">
            I Accept All Cookies
          </Button>
          <Button onClick={() => handleSelectionChange('N')} className="primary w-full sm:w-auto" variant="outline">
            I Do Not Accept Cookies
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CookiePopup;
