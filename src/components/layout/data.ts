import type { NavigationItem } from '@/components/ui/navigation-menu';

const pageLinks = ['Rolex', 'Services', 'About', 'Contact'];

const watchesItems: NavigationItem[] = [
  {
    href: '/rolex',
    imageSrc: '/images/navigation/rolex.jpeg',
    alt: 'Rolex',
    label: 'Rolex',
  },
  {
    href: '/watches/omega',
    imageSrc: '/images/navigation/omega.jpeg',
    alt: 'Omega',
    label: 'Omega',
  },
  {
    href: '/watches/breitling',
    imageSrc: '/images/navigation/breitling.jpeg',
    alt: 'Breitling',
    label: 'Breitling',
  },
  {
    href: '/watches/tag-heuer',
    imageSrc: '/images/navigation/tag-heuer.jpeg',
    alt: 'Tag Heuer',
    label: 'Tag Heuer',
  },
  {
    href: '/watches/pre-owned',
    imageSrc: '/images/navigation/pre-owned.jpeg',
    alt: 'Pre-Owned Watches',
    label: 'Pre-Owned',
  },
];

const jewelleryItems: NavigationItem[] = [
  {
    href: '/jewellery/bespoke',
    imageSrc: '/images/navigation/bespoke.jpeg',
    alt: 'Bespoke Jewellery',
    label: 'Bespoke',
  },
  {
    href: '/jewellery/rings',
    imageSrc: '/images/navigation/rings.jpeg',
    alt: 'Rings',
    label: 'Rings',
  },
  {
    href: '/jewellery/engagement',
    imageSrc: '/images/navigation/engagement.jpeg',
    alt: 'Engagement Rings',
    label: 'Engagement',
  },
  {
    href: '/jewellery/necklaces-and-earrings',
    imageSrc: '/images/navigation/necklaces-and-earrings.jpeg',
    alt: 'Necklaces & Earrings',
    label: 'Necklaces & Earrings',
  },
  {
    href: '/jewellery/fope',
    imageSrc: '/images/navigation/fope.jpeg',
    alt: 'Fope',
    label: 'Fope',
  },
  {
    href: '/jewellery/hans-d-krieger',
    imageSrc: '/images/navigation/hans-d-krieger.jpeg',
    alt: 'Hans D Krieger',
    label: 'Hans D Krieger',
  },
];

export { jewelleryItems, pageLinks, watchesItems };
