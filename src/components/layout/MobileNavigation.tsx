'use client';
import RolexClock from '@/components/layout/rolex/RolexClock';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Sheet, SheetContent, Sheet<PERSON>eader, She<PERSON><PERSON><PERSON>le, SheetTrigger } from '@/components/ui/sheet';
import { Menu, X } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import { jewelleryItems, watchesItems } from './data';
import { ContactInformation, HeaderContactProps } from './Header';

const MobileNavigationContent = ({ items, onLinkClick }: { items: typeof watchesItems; onLinkClick: () => void }) => (
  <div className="grid grid-cols-2 gap-y-4">
    {items.map((item, index) => (
      <Link key={index} href={item.href} className="flex flex-col items-center space-y-2" onClick={onLinkClick}>
        <Image src={item.imageSrc} alt={item.alt} width={130} height={100} className="rounded-sm" />
        <span className="text-center text-sm">{item.label}</span>
      </Link>
    ))}
  </div>
);

const MobileNavigation = ({ phone, email }: HeaderContactProps) => {
  const [open, setOpen] = useState(false);

  const handleClose = () => setOpen(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      {!open && (
        <SheetTrigger className="p-2">
          <Menu className="size-6" />
        </SheetTrigger>
      )}
      {open && (
        <SheetTrigger className="p-2">
          <X className="size-6" />
        </SheetTrigger>
      )}
      <SheetContent
        side="top"
        className="fixed inset-x-0 top-0 z-30 size-full overflow-y-scroll text-foreground-500"
        noOverlay
      >
        <div className="mx-auto max-w-2xl px-4">
          <SheetHeader>
            <SheetTitle className="sr-only text-center">Menu</SheetTitle>
          </SheetHeader>

          <div className="mt-28 flex flex-col">
            <Link
              href="/rolex"
              tabIndex={0}
              onClick={handleClose}
              className="w-full border-b border-gray-200 p-[10px_25px] py-4 text-left font-heading text-lg transition-colors duration-300 hover:text-foreground-500"
            >
              Rolex
            </Link>

            <Accordion defaultValue="" type="single" collapsible className="w-full">
              <AccordionItem value="watches" className="border-b border-gray-200">
                <AccordionTrigger
                  className="font-heading text-lg text-foreground-500 transition-colors duration-300 hover:text-foreground-500 hover:no-underline"
                  autoFocus={false}
                >
                  Watches
                </AccordionTrigger>
                <AccordionContent>
                  <MobileNavigationContent items={watchesItems} onLinkClick={handleClose} />
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="jewellery" className="border-b border-gray-200">
                <AccordionTrigger
                  className="font-heading text-lg text-foreground-500 transition-colors duration-300 hover:text-foreground-500 hover:no-underline"
                  autoFocus={false}
                >
                  Jewellery
                </AccordionTrigger>
                <AccordionContent>
                  <MobileNavigationContent items={jewelleryItems} onLinkClick={handleClose} />
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            <Link
              href="/services"
              onClick={handleClose}
              className="w-full border-b border-gray-200 p-[10px_25px] py-4 text-left font-heading text-lg transition-colors duration-300 hover:text-foreground-500"
            >
              Services
            </Link>

            <Link
              href="/about"
              onClick={handleClose}
              className="w-full border-b border-gray-200 p-[10px_25px] py-4 text-left font-heading text-lg transition-colors duration-300 hover:text-foreground-500"
            >
              About Charles Fox
            </Link>

            <Link
              href="/contact"
              onClick={handleClose}
              className="w-full border-b border-gray-200 p-[10px_25px] py-4 text-left font-heading text-lg transition-colors duration-300 hover:text-foreground-500"
            >
              Contact
            </Link>

            <div className="flex flex-col items-center space-y-2 pt-4 text-center">
              <RolexClock />
              <ContactInformation phone={phone} email={email} />
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MobileNavigation;
