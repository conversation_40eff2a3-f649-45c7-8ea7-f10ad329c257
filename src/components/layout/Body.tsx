'use client';

import { CookiePopupProvider } from '@/contexts/CookiePopupContext';
import { SettingsQueryResult } from '@/sanity/types';
import { GoogleAnalytics } from '@next/third-parties/google';
import { Toaster } from '@/components/ui/toaster';
import CookiePopup from './CookiePopup';
import Footer from './Footer';
import Header from './Header';

export default function Wrapper({
  siteSettings,
  children,
}: {
  siteSettings: SettingsQueryResult;
  children: React.ReactNode;
}) {
  return (
    <>
      <CookiePopupProvider>
        <Header
          logo={
            siteSettings?.brandLogo ?? {
              url: null,
              width: null,
              height: null,
            }
          }
          phone={siteSettings?.contactInfo?.phone as string}
          email={siteSettings?.contactInfo?.email as string}
        />
        {children}
        <Footer
          phone={siteSettings?.contactInfo?.phone as string}
          email={siteSettings?.contactInfo?.email as string}
          address={siteSettings?.contactInfo?.address as string}
        />
        <CookiePopup
          logo={
            siteSettings?.brandLogo ?? {
              url: null,
              width: null,
              height: null,
            }
          }
        />
      </CookiePopupProvider>
      <Toaster />
      <GoogleAnalytics gaId="G-3YXX79FB0Z" />
    </>
  );
}
