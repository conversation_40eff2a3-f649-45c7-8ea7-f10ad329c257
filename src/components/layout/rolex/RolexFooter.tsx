'use client';
import Image from 'next/image';
import { useEffect } from 'react';

const RolexFooter = () => {
  // Set up scroll handling with useEffect
  useEffect(() => {
    // Check if window is available
    if (typeof window === 'undefined') return;

    // Get the button element
    const scrollButton = document.getElementById('scroll-top-button');

    const handleScroll = () => {
      if (window.scrollY > 200) {
        scrollButton?.classList.remove('opacity-0');
      } else {
        scrollButton?.classList.add('opacity-0');
      }
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Cleanup
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    if (typeof window === 'undefined') return;

    const doc = document.documentElement;
    const body = document.body;

    doc.scrollTo({ top: 0, behavior: 'smooth' });
    body.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer className="h-[246px] w-full bg-gradient-to-r from-[#0b3e27] to-[#197149]">
      <div className="flex w-full flex-col items-center justify-center space-y-6 py-8">
        {/* Crown logo */}
        <div className="grid h-[90px] place-items-center text-rolex-beige-400">
          <Image src="/icons/crown.svg" alt="Rolex Crown" width={40} height={40} />
        </div>

        {/* Divider line */}
        <div className="w-full border-t border-rolex-green-400/30"></div>

        {/* Back to top button */}
        <button
          onClick={scrollToTop}
          className="flex h-[80px] flex-col items-center space-y-2 text-white transition-opacity hover:opacity-80"
        >
          <svg viewBox="0 0 24 24" className="size-6" stroke="currentColor" fill="none" strokeWidth="2">
            <path d="M18 15l-6-6-6 6" />
          </svg>
          <span className="legend16-light text-sm font-bold">Back to top</span>
        </button>
      </div>
    </footer>
  );
};

export default RolexFooter;
