'use client';

import { NavigationMenu, NavigationMenuList } from '@radix-ui/react-navigation-menu';
import { ChevronDown, ChevronUp } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import RolexBreadcrumbs from './RolexBreadcrumbs';

import { Button } from '@/components/ui/button';
import { NavigationLoop } from '@/components/ui/navigation-loop';
import { Sheet, SheetContent, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { adjustYearIfBeforeApril } from '@/lib/utils';

export const navItems: string[] = [
  'Discover Rolex',
  'Rolex Watches',
  `New Watches ${adjustYearIfBeforeApril(new Date())}`,
  'Watchmaking',
  'Servicing',
  'World of Rolex',
  'Rolex at Charles Fox',
  'Contact Us',
];

export default function RolexHeader() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className="flex h-[80px] sm:h-[110px] bg-gradient-to-r from-[#0b3e27] to-[#197149] px-[8%] font-rolex">
        <nav className="relative flex w-full items-center justify-between">
          <Link href="/rolex">
            <Image src="/icons/rolex-logo.jpg" width={120} height={60} alt="Rolex Logo" />
          </Link>
          <NavigationMenu>
            <NavigationMenuList className="hidden items-center justify-end space-x-1 lg:flex">
              <NavigationLoop items={navItems} isRolex />
            </NavigationMenuList>
          </NavigationMenu>
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                className="font-bold text-white hover:bg-transparent hover:text-white focus:bg-transparent lg:hidden"
              >
                Menu {isOpen ? <ChevronUp className="ml-0" /> : <ChevronDown className="ml-0" />}
              </Button>
            </SheetTrigger>
            <SheetContent
              side="top"
              className="absolute bottom-0 mt-[256px] h-[calc(100dvh)] border-t-0 bg-gradient-to-r from-[#0b3e27] to-[#197149] p-0 !font-rolex"
              noOverlay
              noButton
            >
              <SheetTitle className="sr-only">Rolex Navigation</SheetTitle>
              <NavigationMenu>
                <NavigationMenuList className="flex flex-col items-start space-y-7 p-6">
                  <NavigationLoop items={navItems} isRolex />
                </NavigationMenuList>
              </NavigationMenu>
            </SheetContent>
          </Sheet>
        </nav>
      </div>
      <RolexBreadcrumbs />
    </>
  );
}
