'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Carousel, CarouselApi, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import { cn } from '@/lib/utils';
import { getAllCategories, getExploringNewWatches } from '@/sanity/lib/helpers';
import { urlFor } from '@/sanity/lib/image';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';
import Autoplay from 'embla-carousel-autoplay';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';
import { useEffect, useState } from 'react';

export interface ExploreCarouselProps {
  heading: string;
  items:
    | string[]
    | {
        title: string;
        image: string | SanityImageSource;
        slug: string;
      }[];
}

export default function ExploreCarousel({ heading, items }: ExploreCarouselProps) {
  const [api, setApi] = React.useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setCount] = useState(0);
  const [filteredItems, setFilteredItems] = useState<
    {
      key: string;
      title: string;
      image: string;
      slug: { current: string };
    }[]
  >([]);

  const pathname = usePathname();

  // Normalize items to a consistent format
  const normalizeItems = (items: any[]) =>
    items.map((item: any) => {
      if (typeof item === 'string') {
        const uri = item
          .toLowerCase()
          .replaceAll(' ', '-')
          .replace(/-\d{4}/, '')
          .replaceAll('rolex-', '');

        return {
          key: item,
          title: item,
          image: `/images/keep-exploring/${uri.includes('discover') ? 'rolex' : uri}.jpg`,
          slug: { current: `/rolex/${uri.includes('discover') ? '' : uri}` },
        };
      } else {
        return {
          key: item.slug.current,
          title: item.title.replace(/Rolex /gi, ''),
          // @ts-expect-error  possible null
          image: urlFor(item.image).url() || '',
          slug: item.slug || '',
        };
      }
    });

  useEffect(() => {
    const fetchFilteredItems = async () => {
      let result: { key: string; title: string; image: string; slug: { current: string } }[] = [];

      // 正则匹配路径规则
      // const showDiscovery = /^\/rolex$|^\/rolex\/[^\/]+$|^\/rolex\/new-watches$/.test(pathname);
      const showNewWatchCategories = /^\/rolex\/new-watches\/[^\/]+$/.test(pathname);
      const showWatchCategory = /^\/rolex\/watches\/[^\/]+$/.test(pathname); // 手表分类页面 (显示其他同类的类别)
      // const showSpecificWatch = /^\/rolex\/watches\/[^\/]+\/[^\/]+$/.test(pathname); // 具体手表页面 (显示该分类下的其他手表)

      // 手表分类页面：显示其他同类的类别
      if (showWatchCategory) {
        const list = await getAllCategories();
        result = normalizeItems(list.filter((item: any) => !item.slug.current.includes('/rolex/new-watches')));
        setFilteredItems(result);
        return;
      }

      if (showNewWatchCategories) {
        const newWatches = await getExploringNewWatches();
        result = normalizeItems(newWatches);
        setFilteredItems(result);
        return;
      }

      result = normalizeItems(items).filter(
        (item: { title: string }) => item.title !== 'World of Rolex' && item.title !== 'Rolex at Charles Fox'
      );
      setFilteredItems(result);
    };

    fetchFilteredItems();
  }, [pathname, items]);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const plugin = React.useRef(Autoplay({ delay: 4000, stopOnInteraction: true }));

  return (
    <div className="relative m-0 mx-auto flex w-full flex-col px-[8%] py-[90px]">
      <h2 className="headline36 mb-2 font-rolex font-semibold text-rolex-brown">{heading}</h2>
      <Carousel
        opts={{
          align: 'start',
          loop: true,
        }}
        setApi={setApi}
        plugins={[plugin.current]}
        className="w-full"
      >
        <CarouselContent className="-ml-1 gap-0 md:-ml-2">
          {filteredItems.map((item) => {
            const isActive = item.slug.current === pathname;
            return (
              <CarouselItem key={item.key} className="flex basis-1/2 justify-center pl-1 md:pl-2 lg:basis-1/4">
                <Link href={item.slug.current} className="w-full">
                  <Card className="w-fulls mx-auto rounded-none border-none bg-transparent shadow-none">
                    <CardContent className="flex flex-col items-center p-0">
                      <div className="relative aspect-[4/3] max-h-[206px] w-full overflow-hidden rounded-none">
                        <Image
                          src={item.image}
                          alt={item.title}
                          fill
                          className="rounded-none object-cover transition-transform duration-300 hover:scale-105"
                        />
                      </div>
                      <h3
                        className={cn(
                          'mt-2 text-lg font-bold font-rolex text-center',
                          isActive ? 'text-rolex-green-600' : 'text-rolex-brown'
                        )}
                      >
                        {item.title}
                      </h3>
                    </CardContent>
                  </Card>
                </Link>
              </CarouselItem>
            );
          })}
        </CarouselContent>
        {filteredItems && filteredItems.length > 0 && (
          <>
            <div className="absolute -left-16 top-[43%] hidden -translate-y-1/2 md:block">
              <Button
                variant="outline"
                size="icon"
                className="size-8 rounded-full bg-white/70 p-0"
                onClick={() => api?.scrollPrev()}
              >
                <ChevronLeft className="size-4" />
                <span className="sr-only">Previous slide</span>
              </Button>
            </div>
            <div className="absolute -right-16 top-[43%] hidden -translate-y-1/2 md:block">
              <Button
                variant="outline"
                size="icon"
                className="size-8 rounded-full bg-white/70 p-0"
                onClick={() => api?.scrollNext()}
              >
                <ChevronRight className="size-4" />
                <span className="sr-only">Next slide</span>
              </Button>
            </div>
          </>
        )}
        <div className="absolute -bottom-6 left-1/2 flex -translate-x-1/2 items-center gap-1">
          {filteredItems.map((_, index: number) => (
            <div
              key={index}
              className={cn(
                'h-[3px] rounded-full transition-all duration-300 ease-in-out',
                index === current - 1 ? 'w-6 bg-rolex-green-600' : 'w-4 bg-gray-300'
              )}
            />
          ))}
        </div>
      </Carousel>
    </div>
  );
}
