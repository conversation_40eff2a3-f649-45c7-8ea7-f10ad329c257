'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

const RolexClock = () => {
  const pathname = usePathname();

  if (!pathname?.includes('omega')) {
    return (
      <Link href="/rolex">
        <iframe
          id="rolex_retailer"
          title="Rolex Official Retailer"
          src="https://static.rolex.com/retailers/clock/?colour=gold&amp;apiKey=d4623e54966ef3265128e1b1fb748099&amp;lang=en"
          scrolling="no"
          style={{
            width: '150px',
            height: '70px',
            border: 0,
            margin: 0,
            padding: 0,
            overflow: 'hidden',
            zIndex: 0,
          }}
        />
      </Link>
    );
  }

  return null;
};

export default RolexClock;
