import { adjustYearIfBeforeApril } from '@/lib/utils';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Fragment } from 'react';

// Utility function to transform path segments
const transformPathSegment = (segment: string) => {
  // Remove "New" from the end of the final breadcrumb if present
  let processedSegment = segment;
  if (processedSegment.endsWith('-new')) {
    processedSegment = processedSegment.replace(/-new$/, '');
  }

  // Remove dashes and capitalize each word
  return processedSegment
    .split('-')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

export default function RolexBreadCrumbs() {
  const pathname = usePathname();
  const currentYear = adjustYearIfBeforeApril(new Date());
  const isNewWatchPath = pathname?.includes('-new');

  // If the path is exactly '/rolex' or starts with '/discover-rolex', return null
  if (pathname === '/rolex' || pathname?.startsWith('/discover-rolex')) {
    return null;
  }

  // Split the path and filter out empty segments
  const pathSegments = pathname?.split('/').filter((segment) => segment && segment !== 'rolex');

  // If no relevant segments, return null
  if (pathSegments?.length === 0) {
    return null;
  }

  // Create breadcrumb items with their corresponding paths
  const breadcrumbItems = [
    { label: 'Discover Rolex', path: '/rolex' },
    ...(pathSegments ?? []).map((segment, index) => {
      // Special handling for watches segment when in new watches path
      if (segment === 'watches' && isNewWatchPath) {
        return {
          label: `New Watches ${currentYear}`,
          path: `/rolex/${(pathSegments ?? []).slice(0, index + 1).join('/')}`,
        };
      }

      return {
        label: transformPathSegment(segment).replaceAll('Ii', 'II'),
        path: `/rolex/${(pathSegments ?? []).slice(0, index + 1).join('/')}`,
      };
    }),
  ];

  return (
    <div className="hidden h-[30px] w-full items-center space-x-1 bg-gradient-to-r from-[#0b3e27] to-[#197149] px-[8%] text-[14px] md:flex">
      {breadcrumbItems.map((item, index) => (
        <Fragment key={index}>
          {index > 0 && (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" className="inline h-2 w-3" fill="white">
              <path d="M12,7.5l-1.3,1.4L4.6,15l-1.5-1.5l6.1-6.1L3,1.4L4.5,0l6.1,6.1l0,0L12,7.5z"></path>
            </svg>
          )}
          {index === breadcrumbItems.length - 1 ? (
            <p className="text-rolex-green-400">{item.label}</p>
          ) : (
            <Link href={item.path} className="text-white transition-all hover:underline hover:opacity-80">
              {item.label}
            </Link>
          )}
        </Fragment>
      ))}
    </div>
  );
}
