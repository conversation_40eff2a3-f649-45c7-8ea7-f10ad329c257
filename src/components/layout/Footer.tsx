import { useCookiePopup } from '@/contexts/CookiePopupContext';
import Image from 'next/image';
import Link from 'next/link';
import { ContactInformation } from './Header';

interface FooterContactProps {
  phone: string;
  email: string;
  address: string;
}

const Footer = ({ phone, email, address }: FooterContactProps) => {
  const { openPopup } = useCookiePopup();

  return (
    <footer className="border-t bg-white py-12">
      <div className="relative mx-auto max-w-screen-2xl px-4 md:px-24">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          {/* Column 1: Logo */}
          <div className="flex flex-col items-center md:items-start">
            <div className="flex flex-col items-center">
              <Link href="/">
                <Image
                  src="/icons/logo-charles-fox.svg"
                  alt="Charles Fox Jewellers Logo"
                  width={212}
                  height={47}
                  className="mb-4 ml-1"
                />
              </Link>
              <Link href="https://gem-a.com/">
                <Image src="/icons/gem-a.png" width={100} height={100} alt="Gem-A Icon" />
              </Link>
            </div>
          </div>

          {/* Column 2: Contact */}
          <div className="text-center md:text-left">
            <h3 className="mb-4 text-lg font-semibold">Contact Us</h3>
            <ContactInformation phone={phone} email={email} />
          </div>

          {/* Column 3: Address */}
          <div className="text-center md:text-left">
            <h3 className="mb-4 text-lg font-semibold">Find Us At</h3>
            <address className="mx-auto max-w-96 whitespace-pre-line font-body not-italic md:mx-0">{address}</address>
            <Link
              href="https://maps.app.goo.gl/nvZu4HMcyMHoY9ZSA"
              target="_blank"
              className="text-blue-600 hover:underline"
            >
              View on Google Maps
            </Link>
          </div>

          {/* Column 4: Navigation */}
          <div className="text-center md:text-left">
            <div className="mb-6">
              <h3 className="mb-4 text-lg font-semibold">Navigation</h3>
              <div className="grid grid-cols-2 justify-center gap-2 md:justify-start">
                <Link href="/watches" className="hover:text-foreground hover:underline">
                  Watches
                </Link>
                <Link href="/rolex" className="hover:text-foreground hover:underline">
                  Rolex
                </Link>
                <Link href="/omega" className="hover:text-foreground hover:underline">
                  Omega Boutique
                </Link>
                <Link href="/bespoke" className="hover:text-foreground hover:underline">
                  Bespoke Jewellery
                </Link>
                <Link href="/services" className="hover:underline">
                  Services
                </Link>
              </div>
            </div>
            <div>
              <h3 className="mb-4 text-lg font-semibold">Legal</h3>
              <div className="grid grid-cols-2 justify-center gap-2 md:justify-start">
                <Link href="/history" className="hover:underline">
                  History
                </Link>
                <Link href="/privacy" className="hover:underline">
                  Privacy Policy
                </Link>
                <Link href="/cookies" className="hover:underline">
                  Cookie Policy
                </Link>
                <span onClick={openPopup} className="cursor-pointer hover:underline">
                  Cookie Settings
                </span>
                <Link href="/sitemap" className="hover:underline">
                  Sitemap
                </Link>
                <Link href="/contact" className="hover:underline">
                  Contact
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="mx-auto mt-8 border-t pt-6 text-center text-gray-500 md:max-w-none">
          © {new Date().getFullYear()} Charles Fox Jewellers.
          <br className="md:hidden" />
          {'  '}All Rights Reserved.
        </div>
      </div>
    </footer>
  );
};

export default Footer;
