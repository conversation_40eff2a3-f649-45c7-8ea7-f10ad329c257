'use client';

import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { cn, OpeningTimes, sortOpeningHours } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';

const CollapsibleOpeningTimes = ({
  openingTimes,
  isCharlesFox,
}: {
  openingTimes: OpeningTimes;
  isCharlesFox?: boolean;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const sortedOpeningHours = sortOpeningHours(openingTimes as OpeningTimes);

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen} className={cn('w-[350px] space-y-2', isCharlesFox && 'w-auto')}>
      <div className="flex items-center justify-between space-x-1">
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm" className={cn('m-0 flex items-center gap-2', isCharlesFox && '-ml-3')}>
            <h4 className={cn('legend14-bold')}>
              Open today{' '}
              <span className={cn('text-[#006039]', isCharlesFox && 'text-foreground-600')}>9:30am - 5:00pm</span>
            </h4>

            {isCharlesFox ? (
              <ChevronDown className={cn('duration-300 transition-transform rotate-0', isOpen && '-rotate-180')} />
            ) : (
              <Image
                src="/icons/triangle.png"
                alt="Arrow Down"
                width={10}
                height={10}
                className={cn('duration-300 transition-transform rotate-0', isOpen && '-rotate-180')}
              />
            )}
            <span className="sr-only">Toggle</span>
          </Button>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent
        className={cn(
          'data-[state=closed]:animate-slide-up data-[state=open]:animate-slide-down',
          'overflow-hidden space-y-2 ml-4'
        )}
      >
        {sortedOpeningHours.map(([day, time]) => (
          <div key={day} className="legend14-bold space-x-1">
            <span>{day.charAt(0).toUpperCase() + day.slice(1, 3)}:</span>
            <span>{time}</span>
          </div>
        ))}
      </CollapsibleContent>
    </Collapsible>
  );
};

export default CollapsibleOpeningTimes;
