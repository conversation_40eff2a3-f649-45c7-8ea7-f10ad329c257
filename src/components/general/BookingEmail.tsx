import { Img } from '@react-email/components';

/* eslint-disable @next/next/no-img-element */
interface AppointmentDetails {
  retailerName?: string;
  date: string;
  time: string;
  address?: string;
  city?: string;
  phone?: string;
  name?: string;
}

export default function RolexAppointmentConfirmation({
  name = 'Sir/<PERSON>am',
  retailerName = 'Charles Fox Jewellers',
  date = 'Tuesday, 9 April 2024',
  time = '2 p.m.',
  address = '21 - 22 The Arcade BH1 2AH',
  city = 'Bournemouth Dorset',
  phone = '01202 557 633',
}: AppointmentDetails) {
  return (
    <table
      width="100%"
      cellPadding="0"
      cellSpacing="0"
      style={{
        maxWidth: '600px',
        margin: '0 auto',
        backgroundColor: '#F9F7F4',
        fontFamily: 'Helvetica, sans-serif',
        color: '#452C1E',
      }}
    >
      <tbody>
        <tr>
          <td align="center" style={{ padding: '20px 0' }}>
            <Img
              src="https://charlesfoxjewellers.com/icons/rolex-logo.jpg"
              alt="Rolex Official Retailer"
              width="200"
              height="80"
              style={{ height: 'auto' }}
            />
          </td>
        </tr>
        <tr>
          <td align="center" style={{ padding: '20px 0' }}>
            <h1
              style={{
                fontSize: '24px',
                fontFamily: 'trajan-pro-3, sans-serif',
              }}
            >
              Confirmation of <br /> your Rolex appointment
            </h1>
          </td>
        </tr>
        <tr>
          <td align="center" style={{ padding: '0 20px 20px' }}>
            <p>Dear {name}</p>
            <p>We are pleased to confirm your appointment at {retailerName}:</p>
            <div
              style={{
                border: '1px solid #e5e7eb',
                padding: '20px',
                margin: '20px 0',
                backgroundColor: 'white',
              }}
            >
              <p>
                {new Date(date).toLocaleDateString()} at {time}
              </p>
              <p>{retailerName}</p>
              <p>
                {address}, {city}
              </p>
            </div>
          </td>
        </tr>
        <tr>
          <td align="center" style={{ padding: '0 20px 20px' }}>
            <a href="https://www.google.com/maps/dir/?api=1&destination=50.720190529578936,-1.8768552164520418">
              <Img
                src="https://charlesfoxjewellers.com/images/appointment-map.jpeg"
                alt="Appointment Map"
                style={{ maxWidth: 300, height: 225 }}
              />
            </a>
          </td>
        </tr>
        <tr>
          <td align="center" style={{ padding: '0 20px 20px' }}>
            <p>
              Thank you for introducing yourself at the entrance upon your arrival. A Rolex sales advisor will be
              available to help you. We thank you for choosing {retailerName}.
              <br />
              Warmest regards,
            </p>
            <p>
              {retailerName}
              <br />
              {address}
              <br />
              {city}
              <br />
              {phone}
              <br />
              <a href={`https://www.charlesfoxjewellers.com/rolex`}>www.retailer.com/rolex</a>
            </p>
          </td>
        </tr>
        <tr>
          <td align="center" style={{ padding: '0 20px 20px' }}>
            <Img src="https://charlesfoxjewellers.com/icons/crown.png" alt="Crown Logo" style={{ width: 42 }} />
          </td>
        </tr>
      </tbody>
    </table>
  );
}
