'use server';

import RolexAppointmentConfirmation from '@/components/general/BookingEmail';
import { render } from '@react-email/render';
import { createTransport } from 'nodemailer';
import React from 'react';
import { createClient } from 'redis';

export type BookingFormData = {
  title: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  country: string;
  date: string;
  time: string;
  type: 'discover' | 'service' | 'initial';
};

export async function submitBooking(formData: BookingFormData) {
  const transporter = createTransport({
    host: 'relay.dnsexit.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });

  try {
    // Store booking in Vercel KV
    const redis = await createClient({ url: process.env.REDIS_URL }).connect();
    const bookingKey = `booking:${Date.now()}:${formData.email}`;
    await redis.set(booking<PERSON>ey, JSON.stringify(formData));

    // Render the email HTML
    const emailHtml = await render(
      React.createElement(RolexAppointmentConfirmation, {
        name: formData.firstName,
        date: formData.date as string,
        time: formData.time as string,
      })
    );

    const userMailOptions = {
      from: `Booking Confirmation <<EMAIL>>`,
      to: [formData.email],
      subject: 'Your Booking Confirmation',
      html: emailHtml,
    };

    const salesMailOptions = {
      from: `"New Booking" <${formData.email}>`,
      to: '<EMAIL>',
      subject: 'New Booking Received',
      html: `
         <h2>New Booking Notification</h2>
         <p><strong>Customer:</strong> ${formData.title} ${formData.firstName} ${formData.lastName}</p>
         <p><strong>Email:</strong> ${formData.email}</p>
         <p><strong>Phone:</strong> ${formData.phone}</p>
         <p><strong>Country:</strong> ${formData.country}</p>
         <p><strong>Date:</strong> ${formData.date}</p>
         <p><strong>Time:</strong> ${formData.time}</p>
         <p><strong>Type:</strong> ${formData.type}</p>
       `,
    };

    await Promise.all([transporter.sendMail(userMailOptions), transporter.sendMail(salesMailOptions)]);

    return {
      success: true,
      message: 'Booking submitted successfully. Confirmation emails have been sent.',
    };
  } catch (error) {
    console.error('Error submitting booking:', error);
    return {
      success: false,
      message: 'Failed to submit booking. Please try again.',
    };
  }
}
