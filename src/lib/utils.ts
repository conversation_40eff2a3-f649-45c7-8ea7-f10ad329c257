import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// Accepts any number of classnames and conditionally merges them.
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export interface OpeningTimes {
  [key: string]: string;
}

// Sorts the opening hours of a business by the days of the week.
export const sortOpeningHours = (openingTimes: OpeningTimes): Array<[string, string]> => {
  const dayOrder = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

  return dayOrder.map((day) => [day, openingTimes[day]] as [string, string]).filter(([, time]) => time);
};

// Adjusts the year by subtracting one if the current date is before April 1st.
export function adjustYearIfBeforeApril(date: Date): number {
  const year = date.getFullYear();
  const month = date.getMonth();
  const day = date.getDate();
  if (month < 3 || (month === 3 && day < 1)) {
    return year - 1;
  }
  return year;
}

export const capitalize = (string: string): string => string.charAt(0).toUpperCase() + string.slice(1);
