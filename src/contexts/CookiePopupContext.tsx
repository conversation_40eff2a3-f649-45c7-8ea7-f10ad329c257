'use client';

import { createContext, useContext, useState } from 'react';

interface CookiePopupContextType {
  isOpen: boolean;
  openPopup: () => void;
  closePopup: () => void;
}

const CookiePopupContext = createContext<CookiePopupContextType | undefined>(undefined);

export function CookiePopupProvider({ children }: { children: React.ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <CookiePopupContext.Provider
      value={{
        isOpen,
        openPopup: () => setIsOpen(true),
        closePopup: () => setIsOpen(false),
      }}
    >
      {children}
    </CookiePopupContext.Provider>
  );
}

export function useCookiePopup() {
  const context = useContext(CookiePopupContext);
  if (context === undefined) {
    throw new Error('useCookiePopup must be used within a CookiePopupProvider');
  }
  return context;
}
