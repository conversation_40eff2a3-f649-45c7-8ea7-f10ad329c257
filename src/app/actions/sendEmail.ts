'use server';

import nodemailer from 'nodemailer';

export async function sendEmail(values: {
  name: string;
  email: string;
  subject: string;
  message: string;
  phoneNumber?: string;
}) {
  const transporter = nodemailer.createTransport({
    host: 'relay.dnsexit.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
    tls: {
      rejectUnauthorized: false,
    },
  });

  const mailOptions = {
    from: '<EMAIL>',
    to: values.email,
    subject: `Website Contact Form: ${values.subject}`,
    text: `
      Name: ${values.name}
      Email: ${values.email}
      Phone Number: ${values.phoneNumber}
      Message: ${values.message}
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    return { success: true };
  } catch (error) {
    return { success: false, error: (error as Error).message };
  }
}

export async function sendRolexContactEmail(values: {
  message: string;
  title: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  country: string;
  city: string;
  storeAddress: string;
  acceptTerms: boolean;
}) {
  const transporter = nodemailer.createTransport({
    host: 'relay.dnsexit.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
    tls: {
      rejectUnauthorized: false,
    },
  });

  const mailOptions = {
    from: '<EMAIL>',
    to: values.email,
    subject: `Rolex Contact Form: Message from ${values.firstName} ${values.lastName}`,
    text: `
      Title: ${values.title}
      Name: ${values.firstName} ${values.lastName}
      Email: ${values.email}
      Phone: ${values.phone || 'Not provided'}
      Country: ${values.country}
      City: ${values.city}
      Store Address: ${values.storeAddress}

      Message:
      ${values.message}

      Terms Accepted: ${values.acceptTerms ? 'Yes' : 'No'}
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    return { success: true };
  } catch (error) {
    return { success: false, error: (error as Error).message };
  }
}
