import { client } from '@/sanity/lib/client';
import type { MetadataRoute } from 'next';

// You can move this to your environment variables
const WEBSITE_HOST_URL = 'https://charlesfoxjewellers.com/'; // Replace with your actual domain

interface SanityDocument {
  _id: string;
  slug: string;
  _updatedAt: string;
  _type: 'page' | 'rolexWatch';
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Fetch all pages and watches from Sanity
  const pages = await client.fetch<SanityDocument[]>(`
    *[(_type == "page" || _type == "rolexWatch")] {
      _id,
      _type,
      "slug": slug.current,
      _updatedAt
    }
  `);

  // Create sitemap entries for each page
  const sitemapEntries = pages.map((page) => {
    // Construct the URL based on the document type
    const path = page._type === 'rolexWatch' ? '/watches' : '';
    const url = `${WEBSITE_HOST_URL}${path}/${page.slug}`;

    return {
      url: url,
      lastModified: new Date(page._updatedAt),
      // Adjust these values based on your needs
      changeFrequency: page._type === 'rolexWatch' ? 'daily' : ('weekly' as 'daily' | 'weekly'),
      priority: page._type === 'rolexWatch' ? 0.8 : 0.5,
    };
  });

  // Add the homepage
  const staticPages = [
    {
      url: WEBSITE_HOST_URL,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
  ];

  return [...staticPages, ...sitemapEntries];
}
