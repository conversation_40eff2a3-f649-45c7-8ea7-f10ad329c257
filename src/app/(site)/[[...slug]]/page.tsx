import Builder from '@/components/Builder';
import { cn } from '@/lib/utils';
import { getPageBySlug } from '@/sanity/lib/helpers';
import { Metadata } from 'next';

export interface PageProps {
  params?: Promise<{
    slug?: string[];
  }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const slugPath = resolvedParams?.slug?.join('/') ?? '';
  const page = await getPageBySlug(`/${slugPath}`);

  return {
    title: page?.title ?? 'Home | Charles Fox Jewellers',
    description: page?.description ?? 'Charles Fox Jewellers',
    openGraph: {
      title: page?.title ?? 'Home | Charles Fox Jewellers',
      description: page?.description ?? 'Charles Fox Jewellers',
      images: [
        {
          url: page?.ogImage?.url ?? '',
          width: page?.ogImage?.width ?? 1200,
          height: page?.ogImage?.height ?? 630,
        },
      ],
    },
  };
}

export default async function Page({ params }: PageProps) {
  const resolvedParams = await params;
  const slugPath = resolvedParams?.slug?.join('/') ?? '';
  const { isProse = false, content = null } = (await getPageBySlug(`/${slugPath}`)) ?? {};

  if (!content) {
    return null;
  }

  return (
    <main className={cn(isProse && 'prose px-4 mx-auto max-w-screen-xl lg:prose-lg pb-8')}>
      <Builder content={content ?? null} />
    </main>
  );
}
