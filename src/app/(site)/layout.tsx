import { getSiteSettings } from '@/sanity/lib/helpers';
import { Metadata, Viewport } from 'next';
import { PageProps } from './[[...slug]]/page';
import ClientLayout from './ClientLayout';
import './globals.css';

export async function generateMetadata(): Promise<Metadata> {
  const siteSettings = await getSiteSettings();
  if (!siteSettings) throw new Error('Site settings not found');

  const { url, title, description, siteName, ogImage } = siteSettings;
  if (!url || !title || !siteName || !ogImage?.url) {
    throw new Error('Required site settings are missing');
  }

  const fullTitle = `${title} | ${siteName}`;

  return {
    title: {
      template: `%s | ${siteName}`,
      absolute: fullTitle,
    },
    metadataBase: new URL(url),
    description: description ?? undefined,
    openGraph: {
      title: fullTitle,
      description: description ?? undefined,
      url: url,
      siteName: siteName,
      images: [{ url: ogImage.url }],
      locale: 'en_GB',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description ?? undefined,
      images: [ogImage.url],
    },
    icons: {
      icon: '/icons/favicon.svg',
    },
    category: 'luxury',
  };
}

export async function generateViewport(): Promise<Viewport> {
  const siteSettings = await getSiteSettings();

  return {
    themeColor: siteSettings?.themeColor,
  };
}

export default async function RootLayout({
  children,
}: Readonly<
  {
    children: React.ReactNode;
  } & PageProps
>) {
  const siteSettings = await getSiteSettings();

  return (
    <html lang="en" className="max-w-[100dvw] overflow-x-hidden">
      <head>
        <link rel="stylesheet" href="https://use.typekit.net/vai2cwb.css" />
      </head>
      <body className="overflow-x-hidden">
        <div className="mx-auto flex min-h-full max-w-[1600px] flex-col bg-white p-0">
          {siteSettings && <ClientLayout siteSettings={siteSettings}>{children}</ClientLayout>}
        </div>
      </body>
    </html>
  );
}
