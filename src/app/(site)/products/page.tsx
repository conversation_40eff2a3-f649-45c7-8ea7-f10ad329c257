'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { ShoppingCart } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface Article {
  Title: string;
  Description: string;
  Price: number;
  StockAvailable: number;
  Brand: string;
  Collection: string;
  Ean: string;
  ReferenceNo: string;
  ArtikelSpecialData?: {
    DiamantenListe?: Array<{
      CaratProDiamant: number;
      Clarity: string;
      Color: string;
      Cut: string;
      CutQualitaet: string;
    }>;
    ArtikelUhr?: {
      Gehaeusebreite?: string;
      Gehaeusematerial?: string;
      Uhrwerk?: string;
      ZifferblattFarbe?: string;
    };
  };
}

const ProductsPage: React.FC = () => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchArticles = async () => {
      try {
        const response = await fetch('http://************:9600/CS_WebService/OsService.svc/articles', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch articles');
        }

        const data = await response.json();
        if (data.Success && Array.isArray(data.data)) {
          setArticles(data.data);
        } else {
          throw new Error('Invalid data format');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchArticles();
  }, []);

  const handleAddToCart = (article: Article) => {
    // Placeholder for cart functionality
    alert(`Added ${article.Title} to cart!`);
  };

  if (loading) {
    return <div className="py-10 text-center">Loading...</div>;
  }

  if (error) {
    return <div className="py-10 text-center text-red-500">{error}</div>;
  }

  return (
    <div className="py-10">
      <h1 className="mb-8 text-center text-3xl font-bold">Our Collection</h1>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        {articles.map((article) => (
          <Card key={article.Ean} className="flex flex-col">
            <CardHeader>
              <div className="flex h-48 w-full items-center justify-center bg-gray-200">
                <span className="text-gray-500">Image Placeholder</span>
              </div>
              <CardTitle className="mt-2 text-lg font-semibold">{article.Title}</CardTitle>
            </CardHeader>
            <CardContent className="grow">
              <p className="line-clamp-3 text-sm text-gray-600">{article.Description}</p>
              <div className="mt-2 space-y-1">
                <p className="text-sm">
                  <span className="font-medium">Brand:</span> {article.Brand || 'N/A'}
                </p>
                <p className="text-sm">
                  <span className="font-medium">Collection:</span> {article.Collection || 'N/A'}
                </p>
                <p className="text-sm">
                  <span className="font-medium">Reference:</span> {article.ReferenceNo}
                </p>
                {article.ArtikelSpecialData?.DiamantenListe?.length ? (
                  <div className="text-sm">
                    <p>
                      <span className="font-medium">Diamond:</span>{' '}
                      {article.ArtikelSpecialData.DiamantenListe[0].CaratProDiamant}ct,{' '}
                      {article.ArtikelSpecialData.DiamantenListe[0].Color},{' '}
                      {article.ArtikelSpecialData.DiamantenListe[0].Clarity},{' '}
                      {article.ArtikelSpecialData.DiamantenListe[0].Cut}
                    </p>
                  </div>
                ) : null}
                {article.ArtikelSpecialData?.ArtikelUhr?.Gehaeusebreite ? (
                  <div className="text-sm">
                    <p>
                      <span className="font-medium">Case:</span> {article.ArtikelSpecialData.ArtikelUhr.Gehaeusebreite}
                      mm, {article.ArtikelSpecialData.ArtikelUhr.Gehaeusematerial}
                    </p>
                    <p>
                      <span className="font-medium">Movement:</span> {article.ArtikelSpecialData.ArtikelUhr.Uhrwerk}
                    </p>
                    <p>
                      <span className="font-medium">Dial:</span>{' '}
                      {article.ArtikelSpecialData.ArtikelUhr.ZifferblattFarbe}
                    </p>
                  </div>
                ) : null}
              </div>
            </CardContent>
            <CardFooter className="flex items-center justify-between">
              <div>
                <p className="text-lg font-bold">£{article.Price.toLocaleString()}</p>
                <Badge variant={article.StockAvailable > 0 ? 'default' : 'destructive'}>
                  {article.StockAvailable > 0 ? 'In Stock' : 'Out of Stock'}
                </Badge>
              </div>
              <Button
                onClick={() => handleAddToCart(article)}
                disabled={article.StockAvailable === 0}
                className="flex items-center gap-2"
              >
                <ShoppingCart className="size-4" />
                Add to Cart
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ProductsPage;
