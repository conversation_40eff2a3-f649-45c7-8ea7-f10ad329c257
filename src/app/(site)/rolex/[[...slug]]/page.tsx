import Builder from '@/components/Builder';
import { placeholders } from '@/lib/placeholders';
import { cn } from '@/lib/utils';
import { getPageBySlug } from '@/sanity/lib/helpers';
import { urlFor } from '@/sanity/lib/image';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';
import { Metadata } from 'next';
import Image from 'next/image';
import { PageProps } from '../../[[...slug]]/page';

// Generates metadata for the page based on the page content.
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const slugPath = resolvedParams?.slug ? `rolex/${resolvedParams.slug.join('/')}` : 'rolex';

  const page = await getPageBySlug(`/${slugPath}`);

  return {
    title: page?.title ?? 'Discover | Rolex at Charles Fox Jewellers',
    description:
      page?.description ??
      "Browse Rolex watches online at Charles Fox Jewellers, an Official Authorised Rolex Retailer of men's and ladies Rolex watches.",
    openGraph: {
      title: page?.title ?? 'Discover | Rolex at Charles Fox Jewellers',
      description:
        page?.description ??
        "Browse Rolex watches online at Charles Fox Jewellers, an Official Authorised Rolex Retailer of men's and ladies Rolex watches.",
      images: [
        {
          url: page?.ogImage?.url ?? '',
          width: page?.ogImage?.width ?? 1200,
          height: page?.ogImage?.height ?? 630,
        },
      ],
    },
  };
}

export default async function Page({ params }: PageProps) {
  const resolvedParams = await params;
  const slugPath = resolvedParams?.slug ? `rolex/${resolvedParams.slug.join('/')}` : 'rolex';

  const {
    isProse = false,
    isRolex = false,
    title = null,
    rolexHeaderImage = null,
    rolexHeaderImageMobile = null,
    content = null,
  } = (await getPageBySlug(`/${slugPath}`)) ?? {};

  if (!content) {
    return null;
  }

  return (
    <>
      {/* <Head> <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} /> </Head> */}
      <main className={cn('rolex', isProse && 'prose lg:prose-lg')}>
        {rolexHeaderImage && (
          <>
            <div className="hidden h-[calc(100vw*8/30.5)] w-full md:block">
              <Image
                src={urlFor(rolexHeaderImage)?.url() ?? placeholders.imageCta.image}
                alt={title as string}
                width={2440}
                height={640}
                className="size-full object-cover"
              />
            </div>
            <div className="w-[calc(100dvw + 16px)] -mx-4 h-[calc(100dvw*8/12)] md:hidden">
              <Image
                src={urlFor(rolexHeaderImageMobile as SanityImageSource)?.url() ?? placeholders.imageCta.image}
                alt={title as string}
                width={393}
                height={524}
                className="h-[calc(100dvw*8/12)] w-full object-cover"
              />
            </div>
          </>
        )}
        <Builder content={content ?? null} isRolex={isRolex as boolean} />
      </main>
    </>
  );
}
