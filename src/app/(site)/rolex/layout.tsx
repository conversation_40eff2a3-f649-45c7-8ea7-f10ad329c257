'use client';

import RolexFooter from '@/components/layout/rolex/RolexFooter';
import RolexHeader, { navItems } from '@/components/layout/rolex/RolexHeader';
import RolexKeepExploring from '@/components/layout/rolex/RolexKeepExploring';
import { usePathname } from 'next/navigation';
import Script from 'next/script';
import { useEffect, useState } from 'react';

export default function RolexLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();
  const [pageType, setPageType] = useState('home page');
  const [pageFamilyName, setPageFamilyName] = useState('');

  const AdobeLaunchScript = () => {
    const isProduction = process.env.NODE_ENV === 'production';
    const scriptSrc = isProduction
      ? '//assets.adobedtm.com/7e3b3fa0902e/7ba12da1470f/launch-5de25e657d80.min.js'
      : '//assets.adobedtm.com/7e3b3fa0902e/7ba12da1470f/launch-73c56043319a-staging.min.js';
    return <Script src={scriptSrc} />;
  };

  useEffect(() => {
    if (!pathname) {
      setPageType('home page');
      setPageFamilyName('');
      return;
    }

    const normalizedPath = pathname.replace(/^\/rolex\/?/, '');
    const slug = normalizedPath.split('/').filter(Boolean);

    if (slug.length === 0) {
      setPageType('home page');
      setPageFamilyName('');
      return;
    }

    const firstSegment = slug[0];
    const slugLength = slug.length;

    if (slugLength === 1) {
      setPageType(firstSegment);
      setPageFamilyName('');
    } else if (firstSegment === 'watches') {
      if (slugLength === 2) {
        setPageType('watch collection');
        setPageFamilyName(slug[1]);
      } else if (slugLength === 3) {
        setPageType('watch page');
        setPageFamilyName(slug[1]);
      } else {
        setPageType('home page');
        setPageFamilyName('');
      }
    } else {
      setPageType('home page');
      setPageFamilyName('');
    }
  }, [pathname]);

  return (
    <>
      <RolexHeader />
      {children}
      <RolexKeepExploring heading="Keep Exploring" items={navItems} />
      <AdobeLaunchScript />
      <Script id="digital-data-layer">
        {`
          var digitalDataLayer = {
            environment: {
              environmentVersion: "V7",
              coBrandedVersion: "Bespoke Light",
            },
            page: {
              pageType: "${pageType}",
              pageFamilyName: "${pageFamilyName}",
            },
          };
        `}
      </Script>
      <RolexFooter />
    </>
  );
}
