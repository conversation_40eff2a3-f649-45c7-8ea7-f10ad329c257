'use client';

import Wrapper from '@/components/layout/Body';
import { RolexBookingSystem } from '@/components/blocks/rolex/RolexBookingSystem';
import { ReactNode } from 'react';

interface ClientLayoutProps {
  children: ReactNode;
  siteSettings: any;
}

export default function ClientLayout({ children, siteSettings }: ClientLayoutProps) {
  return (
    <RolexBookingSystem>
      <Wrapper siteSettings={siteSettings}>{children}</Wrapper>
    </RolexBookingSystem>
  );
}
