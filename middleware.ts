import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // Only run this middleware in production or preview environments
  const vercelEnv = process.env.VERCEL_ENV;
  const isProductionOrPreview = vercelEnv === 'production' || vercelEnv === 'preview';

  if (!isProductionOrPreview) {
    return NextResponse.next();
  }

  const { pathname } = request.nextUrl;

  // Check if the URL matches a Rolex model number pattern
  // Format example: /rolex/watches/day-date-36/M128236-0018
  const modelNumberRegex = /\/rolex\/watches\/[\w-]+\/[A-Z]\d{5,6}-\d{4}$/;

  if (modelNumberRegex.test(pathname)) {
    // Return 404 for model number routes in production/preview
    return new NextResponse(null, { status: 404 });
  }

  return NextResponse.next();
}

export const config = {
  // Matcher for Rolex watch pages only to improve performance
  matcher: ['/rolex/watches/:path*'],
};
