# Charles Fox Jewellers

**Stack**

- Next.js
- TailwindCSS
- shadcn/ui
- Sanity

## Development

1. `git clone https://github.com/wearequantum/charles-fox-jewellers`
2. `vercel link` - follow CLI to link to
   [this Vercel project](https://vercel.com/we-are-quantum/charles-fox-jewellers)
3. `vercel env pull`
4. `pnpm i && pnpm dev`

**Developer Tooling**

- ESLint
  - Stylistic for Formatting
- CommitLint
  - Enforce Conventional Commit's

## Design System

Styles are managed utilising TailwindCSS, there are a few components and utility
classes specified to save time during development.

### Components

**Button**

This targets `button` elements and links with the `.btn` class. There are a few
variants targeting rolex specific buttons, below is a list with their use cases:

- `.rolex-primary`: Default rolex primary button.
- `.rolex-round`: This is for any circular buttons with icons in like on
  `/rolex/contact-use`. This variant has a `.lg` modifier for larger circular
  buttons such as on the slider.
- `.rolex-label`: This has a right arrow in the `::after` pseudoelement it's
  used for the _Learn More_ links and also in the navigation breadcrumbs.
- `.rolex-dashes`: Used for the slider indicators.

### Utilities

There are a variety of utiltiy classes for applying rolex specific text styles
e.g. `.rolex-text-70` which set `font-size: 36px;` and `line-height: 1.1;`.

## Page Builder Component

Found in `src/components/Builder.tsx` this is component is responsible for
generating representations of CMS content. It works by iterating through the
blocks and rendering the component related to each blocks `_type` field.

- The sanity schema types are found ind `src/components/sanity/schemas/blocks`.
- The various frontend blocks for the builder component are found in
  `src/components/blocks`.

**To add a new block**

1. Create a schema type with relevant fields in `src/components/blocks`.
2. Add the type to the `of` array in the `content` field within
   `src/sanity/schemas/pageType.ts`.
3. Add the block types default export to `src/sanity/schemas/index.ts`.
4. Create a component to match the fields in the type in
   `src/components/blocks`.
5. Add the component to the `Builder` component in `src/components/Builder.tsx`.
6. Test by adding content with your new block.
