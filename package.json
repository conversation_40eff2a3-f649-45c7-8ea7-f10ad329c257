{"name": "charles-fox-jewellers", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typegen": "sanity schema extract --path=src/sanity/extract.json && sanity typegen generate", "check": "tsc --noEmit", "prepare": "husky", "format": "prettier --write ."}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@next/third-parties": "^15.1.5", "@portabletext/react": "^3.2.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@react-email/components": "^0.0.35", "@react-email/render": "^1.0.5", "@sanity/asset-utils": "^2.2.1", "@sanity/client": "^6.25.0", "@sanity/icons": "^3.5.7", "@sanity/image-url": "^1.1.0", "@sanity/ui": "^2.11.4", "@sanity/vision": "^3.70.0", "@vercel/kv": "^3.0.0", "@vis.gl/react-google-maps": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "lucide-react": "^0.460.0", "next": "15.2.4", "next-sanity": "^9.8.39", "nodemailer": "^6.9.16", "npm": "^10.9.2", "react": "19.0.0-rc-66855b96-20241106", "react-day-picker": "8.10.1", "react-dom": "19.0.0-rc-66855b96-20241106", "react-hook-form": "^7.54.2", "redis": "^5.1.0", "sanity": "^3.70.0", "schema-dts": "^1.1.2", "styled-components": "^6.1.14", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-animated": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@commitlint/config-conventional": "^19.6.0", "@portabletext/types": "^2.0.13", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.17.14", "@types/nodemailer": "^6.4.17", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "commitlint": "^19.6.1", "eslint": "^8.57.1", "eslint-config-next": "15.0.3", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-tailwindcss": "^3.18.0", "husky": "^9.1.7", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}}